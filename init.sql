-- 初始化数据库脚本

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS store_dashboard CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE store_dashboard;

-- 示例店铺数据（请根据实际情况修改）
-- INSERT INTO stores (shopify_domain, shop_name, shop_email, shop_currency, shop_timezone, feishu_webhook_url, is_active, description, created_at, updated_at) 
-- VALUES 
-- ('example-store.myshopify.com', '示例店铺', '<EMAIL>', 'USD', 'America/Los_Angeles', 'https://open.feishu.cn/open-apis/bot/v2/hook/your-webhook-url', true, '这是一个示例店铺', NOW(), NOW());

-- 注意：请在应用启动后手动添加店铺信息，或者取消注释上面的INSERT语句并填入真实数据
