package router

import (
	controller "x-short-server/controller"
	"x-short-server/middleware"

	"github.com/gin-gonic/gin"
)

func init() {
	api := router(func(e *gin.Engine) {

		e.GET("/api/tag", controller.GetTags)
		e.GET("/api/category", controller.GetCategory)
		e.GET("/api/statics.png", controller.PostStatics)
		e.GET("/api/reports", controller.Reporter)
		e.GET("/api/plan", controller.ListPlan)

		homepage := e.Group("/api/homepage")
		{
			homepage.Use(middleware.UserMiddleware())
			homepage.GET("", controller.GetSysConfig)
			homepage.GET("query", controller.GetSysConfigDetail)
		}
		videoapi := e.Group("/api/video")
		{
			videoapi.Use(middleware.UserMiddleware())
			videoapi.GET("", controller.ListVideo)
			videoapi.GET("episode", controller.GetVideo)
			videoapi.POST("unlock", controller.Unlock)
		}

		userapi := e.Group("/api/user")
		{
			userapi.GET("", controller.UserInfo)
			userapi.POST("", controller.UpdateUser)
			userapi.POST("register", controller.UpdateUser)
			userapi.POST("logout", controller.Logout)
			userapi.POST("delete", controller.UserDelete) // 空实现
			userapi.GET("login-with-user-token", controller.LoginWithUserToken)
			login := userapi.Group("login")
			{
				login.POST("", controller.UserLogin)
				login.GET("facebook", controller.UserLoginFacebook)
				login.GET("callback/facebook", controller.LoginCallbackFacebook)
			}
			history := userapi.Group("history")
			{
				history.GET("", controller.MyHistoryList)
				history.POST("", controller.AddHistory)
				history.DELETE("", controller.DeleteHistory)
			}
			favorite := userapi.Group("favorite")
			{
				favorite.GET("", controller.MyFavoriteList)
				favorite.POST("", controller.AddFavorite)
				favorite.DELETE("", controller.DeleteFavorite)
			}
		}

		pay := e.Group("/api/payment")
		{
			pay.Use(middleware.UserMiddleware())
			pay.GET("", controller.ListPayment)
			pay.POST("pay", controller.PrePay)
			pay.POST("order", controller.PaymentStatus)
			pay.POST("cancel", controller.PaymentCancel)
		}

		webhook := e.Group("/api/webhook")
		{
			webhook.GET("payment/paypal-cancel", controller.CancelPaymentIntent)
			webhook.POST("payment/:id", controller.PaymentWebhook)
			webhook.POST("paypal", controller.PaymentWebhookPayPal)
			webhook.POST("card-3", controller.PaymentWebhookDianPay)
			webhook.POST("card-4", controller.PaymentWebhookMBPayment)
			webhook.GET("card-4-3ds", controller.PaymentWebhookMBPayment)
		}

	})
	routers = append(routers, api)
}
