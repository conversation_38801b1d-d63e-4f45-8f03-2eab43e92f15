package management

import (
	"admin-backend/middleware"
	"github.com/gin-gonic/gin"
)

type TagsRouter struct{}

// InitTagsRouter 初始化 标签表 路由信息
func (s *TagsRouter) InitTagsRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	tagsRouter := Router.Group("tags").Use(middleware.OperationRecord())
	tagsRouterWithoutRecord := Router.Group("tags")
	tagsRouterWithoutAuth := PublicRouter.Group("tags")
	{
		tagsRouter.POST("createTags", tagsApi.CreateTags)             // 新建标签表
		tagsRouter.DELETE("deleteTags", tagsApi.DeleteTags)           // 删除标签表
		tagsRouter.DELETE("deleteTagsByIds", tagsApi.DeleteTagsByIds) // 批量删除标签表
		tagsRouter.PUT("updateTags", tagsApi.UpdateTags)              // 更新标签表
	}
	{
		tagsRouterWithoutRecord.GET("findTags", tagsApi.FindTags)       // 根据ID获取标签表
		tagsRouterWithoutRecord.GET("getTagsList", tagsApi.GetTagsList) // 获取标签表列表
	}
	{
		tagsRouterWithoutAuth.GET("getTagsPublic", tagsApi.GetTagsPublic) // 标签表开放接口
	}
}
