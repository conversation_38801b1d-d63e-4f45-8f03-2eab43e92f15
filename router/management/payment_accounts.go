package management

import (
	"admin-backend/middleware"
	"github.com/gin-gonic/gin"
)

type PaymentAccountsRouter struct{}

// InitPaymentAccountsRouter 初始化 支付账户 路由信息
func (s *PaymentAccountsRouter) InitPaymentAccountsRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	paymentAccountsRouter := Router.Group("paymentAccounts").Use(middleware.OperationRecord())
	paymentAccountsRouterWithoutRecord := Router.Group("paymentAccounts")
	paymentAccountsRouterWithoutAuth := PublicRouter.Group("paymentAccounts")
	{
		paymentAccountsRouter.POST("createPaymentAccounts", paymentAccountsApi.CreatePaymentAccounts)             // 新建支付账户
		paymentAccountsRouter.DELETE("deletePaymentAccounts", paymentAccountsApi.DeletePaymentAccounts)           // 删除支付账户
		paymentAccountsRouter.DELETE("deletePaymentAccountsByIds", paymentAccountsApi.DeletePaymentAccountsByIds) // 批量删除支付账户
		paymentAccountsRouter.PUT("updatePaymentAccounts", paymentAccountsApi.UpdatePaymentAccounts)              // 更新支付账户
	}
	{
		paymentAccountsRouterWithoutRecord.GET("findPaymentAccounts", paymentAccountsApi.FindPaymentAccounts)       // 根据ID获取支付账户
		paymentAccountsRouterWithoutRecord.GET("getPaymentAccountsList", paymentAccountsApi.GetPaymentAccountsList) // 获取支付账户列表
		paymentAccountsRouterWithoutRecord.GET("getPaymentAccountNames", paymentAccountsApi.GetPaymentAccountNames) // 获取支付账户名称
	}
	{
		paymentAccountsRouterWithoutAuth.GET("getPaymentAccountsPublic", paymentAccountsApi.GetPaymentAccountsPublic) // 支付账户开放接口
	}
}
