package management

import (
	"admin-backend/global"
	"admin-backend/model/management"
	managementReq "admin-backend/model/management/request"
	"encoding/json"
	"strconv"

	"go.uber.org/zap"
)

type VideosHiddenService struct{}

// CreateVideosHidden 创建隐藏剧记录
// Author [yourname](https://github.com/yourname)
func (videosHiddenService *VideosHiddenService) CreateVideosHidden(videosHidden *management.VideosHidden) (err error) {
	err = global.MustGetGlobalDBByDBName("businessdb").Create(videosHidden).Error
	return err
}

// DeleteVideosHidden 删除隐藏剧记录
// Author [yourname](https://github.com/yourname)
func (videosHiddenService *VideosHiddenService) DeleteVideosHidden(id string) (err error) {
	err = global.BizWriter().Model(&management.VideosHidden{}).Where("id = ?", id).Delete(&management.VideosHidden{}).Error
	return err
}

// DeleteVideosHiddenByIds 批量删除隐藏剧记录
// Author [yourname](https://github.com/yourname)
func (videosHiddenService *VideosHiddenService) DeleteVideosHiddenByIds(ids []string) (err error) {
	err = global.BizWriter().Delete(&[]management.VideosHidden{}, "id in ?", ids).Error
	return err
}

// UpdateVideosHidden 更新隐藏剧记录
// Author [yourname](https://github.com/yourname)
func (videosHiddenService *VideosHiddenService) UpdateVideosHidden(videosHidden management.VideosHidden) (err error) {
	err = global.BizWriter().Model(&management.VideosHidden{}).Where("id = ?", videosHidden.Id).Updates(&videosHidden).Error
	return err
}

// GetVideosHidden 根据id获取隐藏剧记录
// Author [yourname](https://github.com/yourname)
func (videosHiddenService *VideosHiddenService) GetVideosHidden(id string) (videosHidden management.VideoWithEpisodes, err error) {
	var video management.VideosHidden
	err = global.MustGetGlobalDBByDBName("businessdb").Where("id = ?", id).First(&video).Error
	if err != nil {
		global.Logger.Error("获取隐藏剧失败!", zap.Error(err))
		return
	}

	// 获取剧集
	var episodes []string
	var caption []string
	err = global.MustGetGlobalDBByDBName("businessdb").Table("episodes").Where("vid = ?", id).Order("id ASC").Pluck("s3_url", &episodes).Error
	if err != nil {
		global.Logger.Error("获取剧集失败!", zap.Error(err))
		episodes = make([]string, 0)
	} else {
		for i, episode := range episodes {
			episodes[i] = "https://d1t6z7n6cempmp.cloudfront.net" + episode
		}
	}
	err = global.MustGetGlobalDBByDBName("businessdb").Table("episodes").Where("vid = ?", id).Order("id ASC").Pluck("caption_url", &caption).Error
	if err != nil {
		global.Logger.Error("获取剧集失败!", zap.Error(err))
		caption = make([]string, 0)
	}

	type captionStr struct {
		Lang string `json:"lang"`
		Url  string `json:"url"`
	}

	for i, c := range caption {
		var ls []captionStr
		err = json.Unmarshal([]byte(c), &ls)
		if err != nil {
			caption[i] = ""
			err = nil
		} else {
			for _, l := range ls {
				if l.Lang == "en" {
					caption[i] = "https://d1t6z7n6cempmp.cloudfront.net" + l.Url
					break
				}
				if l.Lang == "en-US" {
					caption[i] = "https://d1t6z7n6cempmp.cloudfront.net" + l.Url
					break
				}
			}
		}
	}

	videosHidden.Episodes = episodes
	videosHidden.Captions = caption
	videosHidden.VideosHidden = video

	return
}

// GetVideosHiddenInfoList 分页获取隐藏剧记录
// Author [yourname](https://github.com/yourname)
func (videosHiddenService *VideosHiddenService) GetVideosHiddenInfoList(info managementReq.VideosHiddenSearch) (list []management.VideosHidden, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.MustGetGlobalDBByDBName("businessdb").Model(&management.VideosHidden{})

	if len(info.VideoName) > 0 {
		vid, err := strconv.ParseUint(info.VideoName, 10, 64)
		if err == nil && vid > 0 {
			db = db.Where("id = ?", vid)
		} else {
			db = db.Where("video_name like ?", "%"+info.VideoName+"%")
		}
	}

	var videosHiddens []management.VideosHidden
	// 如果有条件搜索 下方会自动创建搜索语句
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Order("created_at desc").Limit(limit).Offset(offset)
	}

	if info.Sort != "" {
		db = db.Order(info.Sort + " " + info.SortDirection)
	} else {
		db = db.Order("id DESC")
	}

	err = db.Find(&videosHiddens).Error
	return videosHiddens, total, err
}
func (videosHiddenService *VideosHiddenService) GetVideosHiddenPublic() {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}
