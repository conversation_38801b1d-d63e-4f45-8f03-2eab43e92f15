package management

import (
	"strings"
	"time"

	"admin-backend/global"
	"admin-backend/utils"
	"admin-backend/utils/org"
	"database/sql"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type RealtimeService struct{}

var (
	loc *time.Location
	utc *time.Location
)

func init() {
	loc, _ = time.LoadLocation("Etc/GMT+8") // UTC-8
	utc, _ = time.LoadLocation("Etc/UTC")
}

func parseDateTime(str string, def time.Time) time.Time {
	var (
		t   time.Time = def
		err error
	)
	if len(str) > 0 {
		if strings.Contains(str, "T") {
			t, err = time.ParseInLocation(time.RFC3339, str, time.Local)
		} else {
			t, err = time.ParseInLocation(time.DateTime, str, loc)
		}
		if err != nil {
			global.Logger.Error("Query_error", zap.Error(err), zap.String("str", str))
		}
	}
	return t.In(loc)
}

// 实时数据：15min和今日
func (realtimeService *RealtimeService) GetRealtimeInfo(organizationId uint64) (any, error) {
	db := global.MustGetGlobalDBByDBName("businessdb")
	var (
		todayUserCnt          int64           // 今日用户数
		realtimeUserCnt       int64           // 15min在线用户数
		todayCheckoutCnt      int64           // 今日发起支付数
		realtimeCheckoutCnt   int64           // 15min发起支付数
		todayPaymentCnt       int64           // 今日支付成功数
		realtimePaymentCnt    int64           // 15min支付成功数
		todayPaymentAmount    sql.NullFloat64 // 今日支付金额
		realtimePaymentAmount sql.NullFloat64 // 15min支付金额

		todayBgn      string = utils.GetTodayBeginTime(loc).In(utc).Format(time.DateTime)
		todayEnd      string = utils.GetTodayEndTime(loc).In(utc).Format(time.DateTime)
		fifteenMinAgo string = time.Now().Add(0 - time.Minute*15).Format(time.DateTime)

		err      error
		websites []string
	)

	todayUserCntQuery := db.Table("users").Where("updated_at >= ? and updated_at < ?", todayBgn, todayEnd)
	realtimeUserCntQuery := db.Table("users").Where("updated_at >= ?", fifteenMinAgo)
	todayCheckoutCntQuery := db.Table("payment_order as po").Where("po.created_at >= ? and po.created_at <= ?", todayBgn, todayEnd)
	realtimeCheckoutCntQuery := db.Table("payment_order as po").Where("po.created_at >= ?", fifteenMinAgo)
	todayPaymentCntQuery := db.Table("payment_order as po").Where("po.created_at >= ? AND po.created_at < ? AND po.order_status in ('paid', 'subed','cancel', 'refund')", todayBgn, todayEnd)
	realtimePaymentCntQuery := db.Table("payment_order as po").Where("po.created_at >= ? AND order_status in ('paid', 'subed','cancel', 'refund')", fifteenMinAgo)
	todayPaymentAmountQuery := db.Table("payment_order as po").Select("sum(po.first_amount) as amount").
		Where("po.created_at >= ? AND po.created_at < ? AND po.order_status in ('paid', 'subed','cancel', 'refund')", todayBgn, todayEnd)
	realtimePaymentAmountQuery := db.Table("payment_order as po").Select("sum(po.first_amount) as amount").
		Where("po.created_at >= ? AND po.order_status in ('paid', 'subed','cancel', 'refund')", fifteenMinAgo)

	if organizationId > 0 {
		websites, err = org.GetAllowableWebsitesByOrgId(organizationId)
		if err != nil {
			global.Logger.Error("GetRealtimeInfo_error", zap.Error(err), zap.String("info", "websites"))
		}

		todayUserCntQuery = todayUserCntQuery.Where("website IN (?)", websites)
		realtimeUserCntQuery = realtimeUserCntQuery.Where("website IN (?)", websites)
		todayCheckoutCntQuery = todayCheckoutCntQuery.Joins("left join users as u on po.user_id = u.id").Where("u.website IN (?)", websites)
		realtimeCheckoutCntQuery = realtimeCheckoutCntQuery.Joins("left join users as u on po.user_id = u.id").Where("u.website IN (?)", websites)
		todayPaymentCntQuery = todayPaymentCntQuery.Joins("left join users as u on po.user_id = u.id").Where("u.website IN (?)", websites)
		realtimePaymentCntQuery = realtimePaymentCntQuery.Joins("left join users as u on po.user_id = u.id").Where("u.website IN (?)", websites)
		todayPaymentAmountQuery = todayPaymentAmountQuery.Joins("left join users as u on po.user_id = u.id").Where("u.website IN (?)", websites)
		realtimePaymentAmountQuery = realtimePaymentAmountQuery.Joins("left join users as u on po.user_id = u.id").Where("u.website IN (?)", websites)
	}

	err = todayUserCntQuery.Count(&todayUserCnt).Error
	if err != nil {
		global.Logger.Error("GetRealtimeInfo_error", zap.Error(err), zap.String("info", "todayUserCnt"))
		todayUserCnt = 0
	}

	err = realtimeUserCntQuery.Count(&realtimeUserCnt).Error
	if err != nil {
		global.Logger.Error("GetRealtimeInfo_error", zap.Error(err), zap.String("info", "realtimeUserCnt"))
		realtimeUserCnt = 0
	}

	err = todayCheckoutCntQuery.Count(&todayCheckoutCnt).Error
	if err != nil {
		global.Logger.Error("GetRealtimeInfo_error", zap.Error(err), zap.String("info", "todayCheckoutCnt"))
		todayCheckoutCnt = 0
	}

	err = realtimeCheckoutCntQuery.Count(&realtimeCheckoutCnt).Error
	if err != nil {
		global.Logger.Error("GetRealtimeInfo_error", zap.Error(err), zap.String("info", "realtimeCheckoutCnt"))
		realtimeCheckoutCnt = 0
	}

	err = todayPaymentCntQuery.Count(&todayPaymentCnt).Error
	if err != nil {
		global.Logger.Error("GetRealtimeInfo_error", zap.Error(err), zap.String("info", "todayPaymentCnt"))
		todayPaymentCnt = 0
	}

	err = realtimePaymentCntQuery.Count(&realtimePaymentCnt).Error
	if err != nil {
		global.Logger.Error("GetRealtimeInfo_error", zap.Error(err), zap.String("info", "realtimePaymentCnt"))
		realtimePaymentCnt = 0
	}

	err = todayPaymentAmountQuery.Scan(&todayPaymentAmount).Error
	if err != nil {
		global.Logger.Error("GetRealtimeInfo_error", zap.Error(err), zap.String("info", "todayPaymentAmount"))
		todayPaymentAmount = sql.NullFloat64{Float64: 0, Valid: true}
	}

	err = realtimePaymentAmountQuery.Scan(&realtimePaymentAmount).Error
	if err != nil {
		global.Logger.Error("GetRealtimeInfo_error", zap.Error(err), zap.String("info", "realtimePaymentAmount"))
		realtimePaymentAmount = sql.NullFloat64{Float64: 0, Valid: true}
	}

	return gin.H{
		"todayUserCnt":          todayUserCnt,
		"realtimeUserCnt":       realtimeUserCnt,
		"todayCheckoutCnt":      todayCheckoutCnt,
		"realtimeCheckoutCnt":   realtimeCheckoutCnt,
		"todayPaymentCnt":       todayPaymentCnt,
		"realtimePaymentCnt":    realtimePaymentCnt,
		"todayPaymentAmount":    todayPaymentAmount.Float64 / 100,
		"realtimePaymentAmount": realtimePaymentAmount.Float64 / 100,
	}, nil
}

// 实时订单金额数据：15min和今日
func (realtimeService *RealtimeService) GetRealtimeOrderAmount(organizationId uint64) (any, error) {

	db := global.MustGetGlobalDBByDBName("businessdb")
	var (
		todayBgn      string = utils.GetTodayBeginTime(loc).In(utc).Format(time.DateTime)
		todayEnd      string = utils.GetTodayEndTime(loc).In(utc).Format(time.DateTime)
		fifteenMinAgo string = time.Now().Add(0 - time.Minute*15).Format(time.DateTime)
		err           error
		websites      []string
	)

	var todayCountWithAmount []struct {
		Amount float64 `json:"amount" gorm:"column:amount"`
		Cnt    int64   `json:"cnt" gorm:"column:cnt"`
	}
	todayCountWithAmountQuery := db.Table("payment_order as po").Select("po.first_amount / 100 as amount, count(1) as cnt").
		Where("po.created_at >= ? AND po.created_at < ? AND po.order_status in ('paid', 'subed','cancel', 'refund')", todayBgn, todayEnd).
		Group("po.first_amount")

	var fifteenMinCountWithAmount []struct {
		Amount float64 `json:"amount" gorm:"column:amount"`
		Cnt    int64   `json:"cnt" gorm:"column:cnt"`
	}
	fifteenMinCountWithAmountQuery := db.Table("payment_order as po").Select("po.first_amount / 100 as amount, count(1) as cnt").
		Where("po.created_at >= ? AND po.order_status in ('paid', 'subed','cancel', 'refund')", fifteenMinAgo).
		Group("po.first_amount")

	var todayAmountWithReferer []struct {
		Amount  float64 `json:"amount" gorm:"column:amount"`
		Referer string  `json:"referer" gorm:"column:referer"`
		Cnt     int64   `json:"cnt" gorm:"column:cnt"`
	}
	todayAmountWithRefererQuery := db.Table("payment_order").Select("payment_order.first_amount / 100 as amount, u.referer, count(1) as cnt").
		Joins("left join users u on payment_order.user_id = u.id").
		Where("payment_order.created_at >= ? AND payment_order.created_at < ? AND payment_order.order_status in ('paid', 'subed','cancel', 'refund')", todayBgn, todayEnd).
		Group("payment_order.first_amount, u.referer")

	var fifteenMinAmountWithReferer []struct {
		Amount  float64 `json:"amount" gorm:"column:amount"`
		Referer string  `json:"referer" gorm:"column:referer"`
		Cnt     int64   `json:"cnt" gorm:"column:cnt"`
	}
	fifteenMinAmountWithRefererQuery := db.Table("payment_order").Select("payment_order.first_amount / 100 as amount, u.referer, count(1) as cnt").
		Joins("left join users u on payment_order.user_id = u.id").
		Where("payment_order.created_at >= ? AND payment_order.order_status in ('paid', 'subed','cancel', 'refund')", fifteenMinAgo).
		Group("payment_order.first_amount, u.referer")

	if organizationId > 0 {
		websites, err = org.GetAllowableWebsitesByOrgId(organizationId)
		if err != nil {
			global.Logger.Error("GetRealtimeInfo_error", zap.Error(err), zap.String("info", "websites"))
		}

		todayCountWithAmountQuery = todayCountWithAmountQuery.Joins("left join users as u on po.user_id = u.id").Where("u.website IN (?)", websites)
		fifteenMinCountWithAmountQuery = fifteenMinCountWithAmountQuery.Joins("left join users as u on po.user_id = u.id").Where("u.website IN (?)", websites)
		todayAmountWithRefererQuery = todayAmountWithRefererQuery.Where("u.website IN (?)", websites)
		fifteenMinAmountWithRefererQuery = fifteenMinAmountWithRefererQuery.Where("u.website IN (?)", websites)
	}

	err = todayCountWithAmountQuery.Scan(&todayCountWithAmount).Error
	if err != nil {
		global.Logger.Error("GetRealtimeInfo_error", zap.Error(err), zap.Any("info", todayCountWithAmount))
	}

	err = fifteenMinCountWithAmountQuery.Scan(&fifteenMinCountWithAmount).
		Error
	if err != nil {
		global.Logger.Error("GetRealtimeInfo_error", zap.Error(err), zap.String("info", "realtimePaymentCnt"))
	}

	err = todayAmountWithRefererQuery.Scan(&todayAmountWithReferer).Error
	if err != nil {
		global.Logger.Error("GetRealtimeInfo_error", zap.Error(err), zap.Any("todayAmountWithReferer", todayAmountWithReferer))
	}

	err = fifteenMinAmountWithRefererQuery.Scan(&fifteenMinAmountWithReferer).Error
	if err != nil {
		global.Logger.Error("GetRealtimeInfo_error", zap.Error(err), zap.Any("fifteenMinAmountWithReferer", fifteenMinAmountWithReferer))
	}

	return gin.H{
		"today": gin.H{
			"total":   todayCountWithAmount,
			"referer": todayAmountWithReferer,
		},
		"realtime": gin.H{
			"total":   fifteenMinCountWithAmount,
			"referer": fifteenMinAmountWithReferer,
		},
	}, nil
}

func (realtimeService *RealtimeService) Query(tokens []string, bgn, end string) (any, error) {

	db := global.MustGetGlobalDBByDBName("businessdb")

	type (
		refererCnt struct {
			Referer string `json:"referer" gorm:"column:referer"`
			Cnt     int64  `json:"cnt" gorm:"column:cnt"`
		}
		titleCnt struct {
			Title   string `json:"title" gorm:"column:title"`
			Referer string `json:"referer" gorm:"column:referer"`
			Cnt     int64  `json:"cnt" gorm:"column:cnt"`
		}
	)

	var (
		bgnstr string = parseDateTime(bgn, time.Now().Add(0-time.Minute*15)).In(utc).Format(time.DateTime)
		endstr string = parseDateTime(end, time.Now()).In(utc).Format(time.DateTime)
		err    error
	)

	// 新用户数
	// var newUserCnt []refererCnt
	// // select u.referer,count(1) as cnt from users where created_at >= ? and created_at < ? group by referer
	// err = db.Table("users u").
	// 	Select("u.referer, count(1) as cnt").
	// 	Where("u.created_at >= ? and u.created_at < ?", bgnstr, endstr).Where(token).
	// 	Group("u.referer").
	// 	Order("cnt desc").
	// 	Scan(&newUserCnt).
	// 	Error
	// if err != nil {
	// 	global.Logger.Error("Query_error", zap.Error(err), zap.String("info", "userCnt"))
	// 	newUserCnt = make([]refererCnt, 0)
	// }

	// 活跃用户数
	// var activeUserCnt []refererCnt
	// // select u.referer,count(1) as cnt from users where updated_at >= ? and updated_at < ? group by referer
	// err = db.Table("users u").
	// 	Select("u.referer, count(1) as cnt").
	// 	Where("u.updated_at >= ? and u.updated_at < ?", bgnstr, endstr).Where(token).
	// 	Group("u.referer").
	// 	Order("cnt desc").
	// 	Scan(&activeUserCnt).
	// 	Error
	// if err != nil {
	// 	global.Logger.Error("Query_error", zap.Error(err), zap.String("info", "userCnt"))
	// 	activeUserCnt = make([]refererCnt, 0)
	// }

	// 看剧用户数, token维度
	var watchUserCnt []refererCnt
	// select u.referer,count(1) as cnt from user_play_history p left join users u on p.user_id = u.id where p.updated_at >= ? and p.updated_at < ? group by u.referer
	err = db.Table("user_play_history p").
		Select("u.referer, count(1) as cnt").
		Joins("left join users u on p.user_id = u.id").
		Where("p.updated_at >= ? and p.updated_at < ?", bgnstr, endstr).Where("u.referer IN (?)", tokens).
		Group("u.referer").
		Order("cnt desc").
		Scan(&watchUserCnt).
		Error
	if err != nil {
		global.Logger.Error("Query_error", zap.Error(err), zap.String("info", "userCnt"))
		watchUserCnt = make([]refererCnt, 0)
	}

	// 看剧用户数, 剧集+token维度
	var watchVidCnt []titleCnt
	// select v.video_name as title,u.referer,count(1) as cnt from user_play_history p left join users u on p.user_id = u.id left join videos_hidden v on p.video_id = v.id
	//   where p.updated_at >= ? and p.updated_at < ? group by v.video_name, u.referer
	err = db.Table("user_play_history p").
		Select("v.video_name as title, u.referer, count(1) as cnt").
		Joins("left join users u on p.user_id = u.id").
		Joins("left join videos_hidden v on p.video_id = v.id").
		Where("p.updated_at >= ? and p.updated_at < ?", bgnstr, endstr).Where("u.referer IN (?)", tokens).
		Group("v.video_name, u.referer").
		Order("cnt desc").
		Scan(&watchVidCnt).
		Error
	if err != nil {
		global.Logger.Error("Query_error", zap.Error(err), zap.String("info", "vidCnt"))
		watchVidCnt = make([]titleCnt, 0)
	}

	// 有效用户数
	var validUserCnt []refererCnt
	err = db.Table("user_play_history p").
		Select("u.referer, count(1) as cnt").
		Joins("left join users u on p.user_id = u.id").
		Where("p.updated_at >= ? and p.updated_at < ? and p.episode_num > 1", bgnstr, endstr).Where("u.referer IN (?)", tokens).
		Group("u.referer").
		Order("cnt desc").
		Scan(&validUserCnt).
		Error
	if err != nil {
		global.Logger.Error("Query_error", zap.Error(err), zap.String("info", "validUserCnt"))
		validUserCnt = make([]refererCnt, 0)
	}

	// 前端发起支付数
	var initPaymentCnt []refererCnt
	// select u.referer,count(1) as cnt from statics s left join users u on s.uid = u.id where s.play_at > '2025-03-24 00:00:00' and s.play_at < '2025-03-25 00:00:00' and s.action = 'InitiateCheckout' group by u.referer;
	err = db.Table("statics s").
		Select("u.referer, count(1) as cnt").
		Joins("left join users u on s.uid = u.id").
		Where("s.play_at >= ? and s.play_at < ? and s.action = 'InitiateCheckout'", bgnstr, endstr).Where("u.referer IN (?)", tokens).
		Group("u.referer").
		Order("cnt desc").
		Scan(&initPaymentCnt).
		Error
	if err != nil {
		global.Logger.Error("Query_error", zap.Error(err), zap.String("info", "initPaymentCnt"))
		initPaymentCnt = make([]refererCnt, 0)
	}

	// 发起支付数
	var checkoutCnt []refererCnt
	// select u.referer,count(1) as cnt from payment_order p left join users u on p.user_id = u.id where p.created_at >= ? and p.created_at < ? group by u.referer
	err = db.Table("payment_order p").
		Select("u.referer, count(1) as cnt").
		Joins("left join users u on p.user_id = u.id").
		Where("p.created_at >= ? and p.created_at < ?", bgnstr, endstr).Where("u.referer IN (?)", tokens).
		Group("u.referer").
		Order("cnt desc").
		Scan(&checkoutCnt).
		Error
	if err != nil {
		global.Logger.Error("Query_error", zap.Error(err), zap.String("info", "checkoutCnt"))
		checkoutCnt = make([]refererCnt, 0)
	}

	// 支付成功数
	var paymentCnt []refererCnt
	// select u.referer,count(1) as cnt from payment_order p left join users u on p.user_id = u.id where p.created_at >= ? and p.created_at < ? and p.order_status in ('paid', 'subed') group by u.referer
	err = db.Table("payment_order p").
		Select("u.referer, count(1) as cnt").
		Joins("left join users u on p.user_id = u.id").
		Where("p.created_at >= ? and p.created_at < ? and p.order_status in ('paid', 'subed','cancel', 'refund')", bgnstr, endstr).Where("u.referer IN (?)", tokens).
		Group("u.referer").
		Order("cnt desc").
		Scan(&paymentCnt).
		Error
	if err != nil {
		global.Logger.Error("Query_error", zap.Error(err), zap.String("info", "paymentCnt"))
		paymentCnt = make([]refererCnt, 0)
	}

	// 支付金额
	var paymentAmount []refererCnt
	// select u.referer,sum(p.first_amount) as cnt from payment_order p left join users u on p.user_id = u.id where p.created_at >= ? and p.created_at < ? and p.order_status in ('paid', 'subed') group by u.referer
	err = db.Table("payment_order p").
		Select("u.referer, sum(p.first_amount) as cnt").
		Joins("left join users u on p.user_id = u.id").
		Where("p.created_at >= ? and p.created_at < ? and p.order_status in ('paid', 'subed','cancel', 'refund')", bgnstr, endstr).Where("u.referer IN (?)", tokens).
		Group("u.referer").
		Order("cnt desc").
		Scan(&paymentAmount).
		Error
	if err != nil {
		global.Logger.Error("Query_error", zap.Error(err), zap.String("info", "paymentAmount"))
		paymentAmount = make([]refererCnt, 0)
	}

	var (
		// newUserCntTotal     int64 = 0
		// activeUserCntTotal  int64 = 0
		watchUserCntTotal   int64 = 0
		watchVidCntTotal    int64 = 0
		checkoutCntTotal    int64 = 0
		paymentCntTotal     int64 = 0
		paymentAmountTotal  int64 = 0
		validUserCntTotal   int64 = 0
		initPaymentCntTotal int64 = 0
	)
	// for _, v := range newUserCnt {
	// 	newUserCntTotal += v.Cnt
	// }
	// for _, v := range activeUserCnt {
	// 	activeUserCntTotal += v.Cnt
	// }
	for _, v := range watchUserCnt {
		watchUserCntTotal += v.Cnt
	}
	for _, v := range watchVidCnt {
		watchVidCntTotal += v.Cnt
	}
	for _, v := range checkoutCnt {
		checkoutCntTotal += v.Cnt
	}
	for _, v := range paymentCnt {
		paymentCntTotal += v.Cnt
	}
	for _, v := range paymentAmount {
		paymentAmountTotal += v.Cnt
	}
	for _, v := range validUserCnt {
		validUserCntTotal += v.Cnt
	}
	for _, v := range initPaymentCnt {
		initPaymentCntTotal += v.Cnt
	}

	return gin.H{
		// "newUserCnt":     newUserCnt,
		// "activeUserCnt":  activeUserCnt,
		"watchUserCnt":   watchUserCnt,
		"watchVidCnt":    watchVidCnt,
		"checkoutCnt":    checkoutCnt,
		"paymentCnt":     paymentCnt,
		"paymentAmount":  paymentAmount,
		"validUserCnt":   validUserCnt,
		"initPaymentCnt": initPaymentCnt,
		"total": gin.H{
			// "newUserCntTotal":     newUserCntTotal,
			// "activeUserCntTotal":  activeUserCntTotal,
			"watchUserCntTotal":   watchUserCntTotal,
			"watchVidCntTotal":    watchVidCntTotal,
			"checkoutCntTotal":    checkoutCntTotal,
			"paymentCntTotal":     paymentCntTotal,
			"paymentAmountTotal":  paymentAmountTotal,
			"validUserCntTotal":   validUserCntTotal,
			"initPaymentCntTotal": initPaymentCntTotal,
		},
	}, nil
}
