package airwallex

import (
	"encoding/json"
	"errors"
	"fmt"
	"x-short-server/pkg/logger"
	"x-short-server/util"

	"github.com/gin-gonic/gin"
	"github.com/valyala/fastjson"
	"go.uber.org/zap"
)

func (c *Client) CreateRenewalIntent(amount, currency string, orderId uint64) (string, error) {

	payload := gin.H{
		"amount":            amount,
		"currency":          currency,
		"merchant_order_id": fmt.Sprintf("%d", orderId),
		"request_id":        util.Uuid(),
		"metadata": gin.H{
			"merchant_order_id": fmt.Sprintf("%d", orderId),
			"renewal":           "yes",
		},
	}
	_, bt, err := c.post(payload, endpointCreatePaymentIntent.Format(c.host))
	if err != nil {
		logger.Error("create_airwallex_payment_intent_error", zap.Error(err))
		return "", err
	}

	// use fastjson to decode
	var id = fastjson.GetString(bt, "id")
	logger.Info("airwallex_manual_payment_intent_response", zap.String("id", id))

	return id, nil
}

type ConfirmIntentResp struct {
	Id                    string `json:"id,omitempty"`
	Status                string `json:"status,omitempty"`
	LastestPaymentAttempt struct {
		Status string `json:"status,omitempty"`
	} `json:"latest_payment_attempt,omitempty"`
	Code                         string `json:"code,omitempty"`
	Message                      string `json:"message,omitempty"`
	TraceId                      string `json:"trace_id,omitempty"`
	ProviderOriginalResponseCode string `json:"provider_original_response_code,omitempty"`
	Details                      struct {
		CardBrand            string `json:"card_brand,omitempty"`
		CardType             string `json:"card_type,omitempty"`
		IsCommercial         bool   `json:"is_commercial,omitempty"`
		IssuingBankName      string `json:"issuing_bank_name,omitempty"`
		OriginalResponseCode string `json:"original_response_code,omitempty"`
	} `json:"details,omitempty"`
}

func (c *Client) Confirm(intentId string, consentId string) (bool, *ConfirmIntentResp, error) {

	var resp ConfirmIntentResp

	payload := gin.H{
		"request_id":         util.Uuid(),
		"payment_consent_id": consentId,
	}

	status, bt, err := c.post(payload, endpointConfirmPaymentIntent.Format(c.host, intentId))
	if err != nil {
		logger.Error("confirm_airwallex_payment_intent_error", zap.Error(err))
	}

	if len(bt) > 0 {
		err = json.Unmarshal(bt, &resp)
		if err != nil {
			logger.Error("confirm_airwallex_payment_intent_error", zap.Error(err))
			return false, nil, err
		}
	}
	if status != 200 {
		return false, &resp, errors.New("confirm_airwallex_payment_intent_error")
	}

	logger.Info("airwallex_manual_payment_intent_response", zap.String("id", resp.Id), zap.String("status", resp.Status), zap.String("body", string(bt)), zap.Any("req", payload))

	if resp.Status == "SUCCEEDED" && resp.LastestPaymentAttempt.Status == "AUTHORIZED" {
		return true, &resp, nil
	}

	return false, &resp, nil
}
