package airwallex

import (
	"errors"
	"x-short-server/pkg/logger"
	"x-short-server/pkg/rsa"
	"x-short-server/util"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

const (
	PaymentIntentSuccess   = "payment_intent.succeeded"
	PaymentIntentCancelled = "payment_intent.cancelled"
	PaymentConsentVerified = "payment_consent.verified"
	PaymentConsentUpdated  = "payment_consent.updated"
	FraudMerchantNotified  = "fraud.merchant_notified"
	SubscriptionCreated    = "subscription.created"
	SubscriptionUpdated    = "subscription.updated"
	SubscriptionCancelled  = "subscription.cancelled"
	DisputeRequiredAction  = "payment_dispute.requires_response"
)

func (s *Client) VerifyWebhookSignature(c *gin.Context, webhookSecret string) error {
	var (
		sign = c.GetHeader("x-signature")
		ts   = c.GetHeader("x-timestamp")
		err  error
	)
	if sign == "" || ts == "" {
		logger.Error("airwallex_webhook_error", zap.String("sign", sign), zap.String("ts", ts))
		return errors.New("signature or timestamp not found")
	}

	bodyBytes := util.CopyRawData(c)
	if bodyBytes == nil {
		logger.Error("airwallex_webhook_error", zap.Error(err))
		return err
	}
	bodyString := string(bodyBytes)
	str := []byte(ts + bodyString)
	if rsa.VerifyHMACSign(str, []byte(sign), []byte(webhookSecret)) {
		return nil
	}
	logger.Error("airwallex_webhook_error", zap.String("body", bodyString), zap.String("sign", sign), zap.String("ts", ts))
	return errors.New("signature verification failed")
}
