package service

import (
	"errors"
	"time"
	"x-short-server/model"
	"x-short-server/pkg/constants"
	"x-short-server/pkg/logger"
	"x-short-server/pkg/xerr"
	"x-short-server/util"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func (s *UserService) GetUserFromToken(c *gin.Context, token string) (*model.User, error) {

	// 解出 jwt 中 的 user_id 字段
	userID, err := sessionService.ParseUserIdFromToken(c, token)
	if err != nil {
		logger.Error("cannot_get_user_id_from_token", zap.String("token", token), zap.Error(err))
		return nil, errors.New("verify_jwt_failed")
	}

	// 从 db 查询
	hit, user, err := model.NewUserModel().FindByUserId(userID)
	if !hit {
		logger.Error("user_not_found", zap.Uint64("user_id", userID), zap.String("token", token), zap.Error(err))
		return nil, errors.New("user_not_found")
	}

	return user, nil
}

func (s *UserService) GenerateAnonymousUser(c *gin.Context) (*model.User, error) {
	var IsAdvance, referertoken = s.IsAdvanceUser(c)
	// 生成一个匿名用户
	user := &model.User{
		Id:              util.GenerateUint64ID(),
		Website:         c.Request.Host,
		Email:           "",
		Username:        "",
		Avatar:          "",
		Sid:             "",
		Password:        "",
		Referer:         referertoken,
		RefererRaw:      c.Request.Referer(),
		LastLoginIp:     c.ClientIP(),
		Language:        "en-US",
		IsEmailVerified: util.Bool(false),
		IsAnonymous:     util.Bool(true),
		IsAdvance:       &IsAdvance,
		Region:          util.GetRegionFromHttpHeader(c),
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	if len(referertoken) > 0 {
		c.Header(constants.HeaderUserRefererToken, referertoken)
		c.SetCookie(constants.CookieUserRefererToken, referertoken, int(constants.SessionMaxAge), "/", "", false, true)
	}

	// 保存到 db
	err := model.NewUserModel().Create(user)
	if err != nil {
		logger.Error("create_user_failed", zap.Any("user", user), zap.Error(err))
		return nil, errors.New("create_user_failed")
	}

	go func(c *gin.Context, u *model.User) {
		sessionService.UpdateUtmSource(c, u)
	}(c.Copy(), user)

	return user, nil
}

// IsAdvanceUser 判断是否是广告用户
// 需要同时满足:
// 1. referer_token 在 admin_referer_token 表中
// 2. ua 是fb 或 tk
// 3. ip 是对应国家的ip
// 4. fbclid 或 ttclid 存在
func (s *UserService) IsAdvanceUser(c *gin.Context) (bool, string) {
	referer_token := s.GetRefererToken(c)
	if referer_token == "A798955E-7071-4B16-AF0B-DD98BD22B818" {
		return true, referer_token
	}

	// var ua = false
	// // fb
	// if util.IsFacebookApp(c.Request.UserAgent()) {
	// 	fbclid := c.Query("fbclid")
	// 	if len(fbclid) == 0 {
	// 		fbclid = c.GetHeader("Fbclid")
	// 	}
	// 	if !util.IsFbClickId(fbclid, true) {
	// 		return false, ""
	// 	}
	// 	ua = true
	// }

	// // tk
	// tk := c.Query("ttclid")
	// if len(tk) > 0 {
	// 	ua = true
	// }

	// // ua
	// if !ua {
	// 	return false, ""
	// }

	// // 归属国家
	// area := util.GetRegionFromHttpHeader(c)
	// countries := model.NewSystemConfig().GetWithCache(model.SystemConfigKeyDeliveryCountry)
	// if len(countries) == 0 || !strings.Contains(countries, area) {
	// 	return false, ""
	// }

	// referer_token
	if len(referer_token) == 0 {
		return false, ""
	}

	// 从 db 查询
	hit, _, _ := model.NewAdminRefererTokenModel().FindByRefererToken(referer_token)
	return hit, referer_token
}

func (s *UserService) GetUserToken(c *gin.Context) string {

	var (
		user_token string
		err        error
	)

	// 从 cookie 取
	user_token, err = c.Cookie(constants.CookieUserToken)
	if err != nil && !xerr.IsGinCookieNotFound(err) {
		logger.Error("cannot_get_user_token_from_cookie", zap.Error(err))
	}

	if len(user_token) > 0 {
		return user_token
	}

	// 从 header 取
	return c.GetHeader(constants.HeaderUserToken)
}

func (s *UserService) GetRefererToken(c *gin.Context) string {

	var (
		referer_token string
		err           error
	)

	// 先从 header 取
	referer_token = c.GetHeader(constants.HeaderUserRefererToken)
	if len(referer_token) > 0 {
		return referer_token
	}

	// 从 cookie 取
	referer_token, err = c.Cookie(constants.CookieUserRefererToken)
	if err != nil && !xerr.IsGinCookieNotFound(err) {
		logger.Error("cannot_get_referer_token_from_cookie", zap.Error(err))
	}

	if len(referer_token) > 0 {
		return referer_token
	}

	// 从 header 取
	return c.GetHeader(constants.HeaderUserRefererToken)
}
