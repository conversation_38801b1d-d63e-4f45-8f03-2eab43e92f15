package service

import (
	"encoding/json"
	"fmt"
	"time"
	"x-short-server/model"
	"x-short-server/pkg/logger"
	"x-short-server/pkg/redis"
	"x-short-server/pkg/xerr"
	"x-short-server/util"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type StaticsService struct{}

func NewStaticsService() *StaticsService {
	return &StaticsService{}
}

var StandardEventMaps = map[string]string{
	"plan_view":        string(EventNameAddToCart),
	"AddToCart":        string(EventNameAddToCart),
	"InitiateCheckout": string(EventNameInitPayment),
	"AddPaymentInfo":   string(EventNameFillPayment),
	"x_page_view":      string(EventNameVideoView),
	"x_purchase":       string(EventNamePurchase),
}

func (s *StaticsService) PostStatics(c *gin.Context, user *model.User, eventName, eventId string) error {
	var ok bool

	// 转为标准事件
	eventName, ok = StandardEventMaps[eventName]
	if !ok {
		logger.WithCase("service.statics.PostStatics").Info("event_name not found", zap.String("event_name", eventName), zap.String("event_id", eventId))
		return fmt.Errorf("event_name not found")
	}

	// 生成eventId
	if eventId == "" {
		eventId = fmt.Sprintf("%s_%d", eventName, time.Now().UnixMilli())
	}

	// 保存统计数据
	m := model.Statics{
		Id:          util.GenerateUint64ID(),
		Action:      eventName,
		EventId:     eventId,
		PlayAt:      time.Now(),
		Uid:         user.Id,
		UserAgent:   c.Request.UserAgent(),
		Ip:          c.ClientIP(),
		Fbclid:      util.GetFbclid(c),
		UtmSource:   util.GetUtmSource(c),
		UtmMedium:   util.GetUtmMedium(c),
		UtmCampaign: util.GetUtmId(c),
		UtmTerm:     util.GetUtmTerm(c),
		UtmContent:  util.GetUtmContent(c),
	}
	rawQuery := c.Request.URL.RawQuery
	if len(rawQuery) > 60000 {
		logger.WithCase("service.statics.PostStatics").Error("rawQuery is too long", zap.String("rawQuery", rawQuery))
		rawQuery = rawQuery[:60000]
	}
	rawReferer := c.Request.Referer()
	if len(rawReferer) > 5000 {
		logger.WithCase("service.statics.PostStatics").Error("rawReferer is too long", zap.String("rawReferer", rawReferer))
		rawReferer = rawReferer[:5000]
	}
	raw := gin.H{
		"raw_query":   rawQuery,
		"raw_referer": rawReferer,
	}
	bt, err := json.Marshal(raw)
	if err != nil {
		logger.WithCase("service.statics.PostStatics").Error("json.Marshal failed", zap.Error(err))
		return err
	}
	m.Raw = string(bt)
	model.DB().Model(&m).Create(&m)

	// 推送像素事件
	if AcceptEvents.Contains(eventName) && eventName != string(EventNamePurchase) {
		traceService.TriggerOtherEvent(c, eventName, eventId, &m.PlayAt, user)
	}

	return nil
}

func (s *StaticsService) GetFacebookPixel(c *gin.Context, admin bool) ([]model.SystemConfig, error) {
	return model.NewSystemConfig().GetFacebookPixel(admin)
}

func (s *StaticsService) GetGoogleAnalytics(c *gin.Context, admin bool) ([]model.SystemConfig, error) {
	return model.NewSystemConfig().GetGoogleAnalytics(admin)
}

func (s *StaticsService) CreateSystemConfig(c *gin.Context, key, value string) (model.SystemConfig, error) {
	if key == model.SystemConfigKeyFacebookPixelID {
		return model.NewSystemConfig().CreateFacebookPixel(value)
	}
	if key == model.SystemConfigKeyGoogleAnalytics {
		return model.NewSystemConfig().CreateGoogleAnalytics(value)
	}
	if key == model.SystemConfigKeyTikTokPixel {
		return model.NewSystemConfig().CreatePixel(value, model.SystemConfigKeyTikTokPixel)
	}
	if key == model.SystemConfigKeyBigoPixel {
		return model.NewSystemConfig().CreatePixel(value, model.SystemConfigKeyBigoPixel)
	}
	if key == model.SystemConfigKeyPinterestPixel {
		return model.NewSystemConfig().CreatePixel(value, model.SystemConfigKeyPinterestPixel)
	}
	return model.SystemConfig{}, ErrInvalidSystemConfigKey
}

func (s *StaticsService) GetPixel(c *gin.Context, admin bool) ([]model.SystemConfig, error) {

	var (
		cacheKey redis.CacheKey
		err      error
		sc       []model.SystemConfig
	)

	if admin {
		cacheKey = redis.NewCacheKey("x-short-server", "service", "get-pixel-admin", c.Request.Host)
	} else {
		cacheKey = redis.NewCacheKey("x-short-server", "service", "get-pixel", c.Request.Host)
	}

	cache := cacheKey.Get()
	if len(cache) > 0 {
		err = json.Unmarshal([]byte(cache), &sc)
		if err == nil {
			return sc, nil
		}
	}

	sc, err = model.NewSystemConfig().GetPixel(admin, []string{model.SystemConfigKeyFacebookPixelID, model.SystemConfigKeyGoogleAnalytics, model.SystemConfigKeyTikTokPixel, model.SystemConfigKeyBigoPixel, model.SystemConfigKeyPinterestPixel}, c.Request.Host)
	if err != nil {
		if xerr.IsGormRecordNotFound(err) {
			return nil, nil
		}
		return nil, err
	}

	cacheKey.SetObject(sc, 5*time.Minute)

	return sc, nil
}
