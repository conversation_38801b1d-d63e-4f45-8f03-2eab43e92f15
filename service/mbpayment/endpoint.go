package mbpayment

import (
	"fmt"
	"strings"
)

type endpoint string

const (
	endpointCreateOrder = endpoint("/MBPayment/api/transaction")
)

func (e endpoint) Format(host string, args ...interface{}) string {
	if len(args) == 0 {
		return fmt.Sprintf("%s%s", host, e)
	}
	if len(args) == strings.Count(string(e), "%s") {
		return fmt.Sprintf("%s%s", host, fmt.Sprintf(string(e), args...))
	}
	return fmt.Sprintf("%s%s", host, e)
}
