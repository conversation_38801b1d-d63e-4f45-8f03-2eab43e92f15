package mbpayment

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"net/url"
	"strings"
	"x-short-server/pkg/logger"
	"x-short-server/pkg/rsa"
	"x-short-server/util"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type Client struct {
	merchantId string
	terminalNo string
	Key        string
	host       string
	httpclient *http.Client
}

func NewClient(merchantId string, clientId, clientSecret string, isSandbox bool, httpclient *http.Client) *Client {
	host := "https://gateway.ssltrustpayment.com"
	// if isSandbox {
	// 	host = "https://gateway.ssltrustpayment.com"
	// }
	return &Client{
		merchantId: merchantId,
		terminalNo: clientId,
		Key:        clientSecret,
		host:       host,
		httpclient: httpclient,
	}
}

func (c *Client) post(u string, data string) []byte {

	var (
		body []byte
		err  error
	)

	body = []byte(data)

	req, err := http.NewRequest("POST", u, bytes.NewBuffer(body))
	if err != nil {
		logger.Error("mbpayment_post_request_error", zap.Error(err))
		return nil
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	resp, err := c.httpclient.Do(req)
	if err != nil {
		logger.Error("mbpayment_post_request_error", zap.Error(err))
		return nil
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error("mbpayment_post_read_error", zap.Error(err))
		return nil
	}
	logger.Info("mbpayment_post_response",
		zap.Int("status_code", resp.StatusCode),
		zap.String("url", u),
		zap.ByteString("resp_body", respBody),
		zap.ByteString("req_body", []byte(util.MaskCardNumber(string(body)))),
		zap.Any("header", resp.Header),
	)

	return respBody
}

type CreateOrderRequest struct {
	MerchantNo      string `json:"MerchantNo,omitempty"`      // merchant id
	TerminalNo      string `json:"TerminalNo,omitempty"`      // client id
	TransactionType string `json:"TransactionType,omitempty"` // 交易类型, sales
	Type            string `json:"Type,omitempty"`            // 接口类型, 1普通接口、2 app sdk、3快捷支付、4 虚拟
	Model           string `json:"Model,omitempty"`           // 交易模式, M
	Encryption      string `json:"Encryption,omitempty"`      // 固定SHA256
	CharacterSet    string `json:"CharacterSet,omitempty"`    // 固定UTF8
	Amount          string `json:"Amount,omitempty"`          // 支付金额
	CurrencyCode    string `json:"CurrencyCode,omitempty"`    // 交易币种 USD
	OrderNo         string `json:"OrderNo,omitempty"`         // 网店订单编号
	Remark          string `json:"Remark,omitempty"`          // 商户预留字段
	ReturnURL       string `json:"ReturnURL,omitempty"`       // 同步返回支付结果到商户地址 return_url
	AsynURL         string `json:"asyn_url,omitempty"`        // 异步返回支付结果到商户地址 asyn_url
	SourceURL       string `json:"source_url,omitempty"`      // 真实交易网站 user.website
	TransactionURL  string `json:"TransactionURL,omitempty"`  // 交易网站
	BillCountry     string `json:"BillCountry,omitempty"`     // 账单国家
	BillState       string `json:"BillState,omitempty"`       // 账单州(省)
	BillCity        string `json:"BillCity,omitempty"`        // 账单城市
	BillAddress     string `json:"BillAddress,omitempty"`     // 账单详细地址
	BillZipCode     string `json:"BillZipCode,omitempty"`     // 账单邮编
	IpAddress       string `json:"IpAddress,omitempty"`       // 客户的IP
	BillFullName    string `json:"BillFullName,omitempty"`    // 持卡人姓名
	BillPhone       string `json:"BillPhone,omitempty"`       // 持卡人电话
	ShipCountry     string `json:"ShipCountry,omitempty"`     // 收货国家
	ShipState       string `json:"ShipState,omitempty"`       // 收货州(省)
	ShipCity        string `json:"ShipCity,omitempty"`        // 收货城市
	ShipAddress     string `json:"ShipAddress,omitempty"`     // 收货地址
	ShipZipCode     string `json:"ShipZipCode,omitempty"`     // 收货地址邮编
	ShipEmail       string `json:"ShipEmail,omitempty"`       // 收货邮箱地址
	ShipPhone       string `json:"ShipPhone,omitempty"`       // 收货人电话
	ShipFullName    string `json:"ShipFullName,omitempty"`    // 收货人姓名
	SignCode        string `json:"SignCode,omitempty"`        // 验证参数
	CardNo          string `json:"CardNo,omitempty"`          // 卡号
	ExpYear         string `json:"ExpYear,omitempty"`         // 有效年
	ExpMonth        string `json:"ExpMonth,omitempty"`        // 有效月
	CVV             string `json:"CVV,omitempty"`             // cvv
	GoodsJson       string `json:"GoodsJson,omitempty"`       // 商品信息
	WebInfo         string `json:"webInfo,omitempty"`         // 客户端浏览器信息
	Language        string `json:"language,omitempty"`        // 语言
}

func (req *CreateOrderRequest) EncodeFormData() string {
	values := url.Values{}
	values.Set("MerchantNo", req.MerchantNo)
	values.Set("TerminalNo", req.TerminalNo)
	values.Set("TransactionType", req.TransactionType)
	values.Set("Type", req.Type)
	values.Set("Model", req.Model)
	values.Set("Encryption", req.Encryption)
	values.Set("CharacterSet", req.CharacterSet)
	values.Set("Amount", req.Amount)
	values.Set("CurrencyCode", req.CurrencyCode)
	values.Set("OrderNo", req.OrderNo)
	values.Set("Remark", req.Remark)
	values.Set("ReturnURL", req.ReturnURL)
	values.Set("asyn_url", req.AsynURL)
	values.Set("source_url", req.SourceURL)
	values.Set("TransactionURL", req.TransactionURL)
	values.Set("BillCountry", req.BillCountry)
	values.Set("BillState", req.BillState)
	values.Set("BillCity", req.BillCity)
	values.Set("BillAddress", req.BillAddress)
	values.Set("BillZipCode", req.BillZipCode)
	values.Set("IpAddress", req.IpAddress)
	values.Set("BillFullName", req.BillFullName)
	values.Set("BillPhone", req.BillPhone)
	values.Set("ShipCountry", req.ShipCountry)
	values.Set("ShipState", req.ShipState)
	values.Set("ShipCity", req.ShipCity)
	values.Set("ShipAddress", req.ShipAddress)
	values.Set("ShipZipCode", req.ShipZipCode)
	values.Set("ShipEmail", req.ShipEmail)
	values.Set("ShipPhone", req.ShipPhone)
	values.Set("ShipFullName", req.ShipFullName)
	values.Set("SignCode", req.SignCode)
	values.Set("CardNo", req.CardNo)
	values.Set("ExpYear", req.ExpYear)
	values.Set("ExpMonth", req.ExpMonth)
	values.Set("CVV", req.CVV)
	values.Set("GoodsJson", req.GoodsJson)
	values.Set("webInfo", req.WebInfo)
	values.Set("language", req.Language)
	return values.Encode()
}

type Order struct {
	OrderNo      string  `json:"orderNo,omitempty"`      // 网店订单编号
	MerchantNo   string  `json:"merchantNo,omitempty"`   // 商户号
	TerminalNo   string  `json:"terminalNo,omitempty"`   // 终端号
	TradeNo      string  `json:"tradeNo,omitempty"`      // 支付交易流水号
	CurrencyCode string  `json:"currencyCode,omitempty"` // 订单币种
	Amount       string  `json:"amount,omitempty"`       // 订单金额
	ErrorCode    string  `json:"erroCode,omitempty"`     // 成功标志
	ErrorMsg     string  `json:"erroMsg,omitempty"`      // 返回交易成功失败信息
	RedirectUrl  *string `json:"redirect_url,omitempty"` // 当使用3D接口时返回
	RespCode     string  `json:"respCode,omitempty"`     // 3D验证结果
}

func (o *Order) Need3DS() bool {
	return (o.ErrorCode == "02" || o.ErrorCode == "03" || o.ErrorCode == "01") && o.RedirectUrl != nil && len(*o.RedirectUrl) > 0
}

func (c *Client) CreateOrder(ctx context.Context, data CreateOrderRequest) (success bool, pi *Order, err error) {

	data.MerchantNo = c.merchantId
	data.TerminalNo = c.terminalNo
	data.Encryption = "SHA256"
	data.CharacterSet = "UTF8"
	data.TransactionType = "sales"
	data.Type = "1"
	data.Model = "M"
	data.Language = "En"
	data.CurrencyCode = "USD"
	data.SignCode = c.generateSign(data)

	url := endpointCreateOrder.Format(c.host)

	resp := c.post(url, data.EncodeFormData())
	if len(resp) == 0 {
		logger.Error("mbpayment_post_response_nil")
		return false, nil, nil
	}

	var res Order
	err = json.Unmarshal(resp, &res)
	if err != nil {
		logger.Error("mbpayment_post_unmarshal_error", zap.Error(err), zap.ByteString("resp_body", resp))
		return false, nil, err
	}

	if res.ErrorCode == "00" {
		return true, &res, nil
	}

	if res.Need3DS() {
		return true, &res, nil
	}

	return false, &res, errors.New(res.ErrorMsg)
}

func (c *Client) VerifyWebhook(ctx *gin.Context, queryString url.Values) (bool, error) {
	// amount=22.9900&currencyCode=USD&hashcode=59c65948feb278c791775fa2cbbde4432339756dc570600d363841a6d51ebea1&
	// merchantNo=1001&orderNo=43188832836788224&respCode=00&respMsg=Test+Success&
	// shoppertoken=0abc4298d39c94f08df9045f064f18de80c6acd9f413da7efdc7dc2ccd5148d1&
	// terminalNo=88001&tradeNo=MBF2504301217003853&transType=sale
	var (
		merchantNo = queryString.Get("merchantNo")
		terminalNo = queryString.Get("terminalNo")
		orderNo    = queryString.Get("orderNo")
		tradeNo    = queryString.Get("tradeNo")
		amount     = queryString.Get("amount")
		errorCode  = queryString.Get("respCode")
		sign       = queryString.Get("hashcode")
	)

	if len(merchantNo) == 0 || len(terminalNo) == 0 || len(orderNo) == 0 || len(tradeNo) == 0 || len(amount) == 0 || len(errorCode) == 0 {
		return false, nil
	}

	payload := []byte(merchantNo + "&" + terminalNo + "&" + orderNo + "&" + tradeNo + "&" + amount + "&" + errorCode + "&" + c.Key)

	ret := rsa.VerifySHA256Sign(payload, []byte(sign))
	if !ret {
		logger.Error("mbpayment_verify_signature_error", zap.String("payload", string(payload)), zap.String("sign", sign))
		return false, errors.New("signature error")
	}

	return true, nil
}

func (c *Client) generateSign(body CreateOrderRequest) string {

	// Encryption&CharacterSet&MerchantNo&TerminalNo&OrderNo&CurrencyCode&Amount&IpAddress&Key

	var payload = []string{
		body.Encryption,
		body.CharacterSet,
		c.merchantId,
		c.terminalNo,
		body.OrderNo,
		body.CurrencyCode,
		body.Amount,
		body.IpAddress,
		c.Key,
	}

	var payloadStr = strings.Join(payload, "&")
	sign := rsa.GenerateSHA256Sign([]byte(payloadStr))
	if sign == "" {
		logger.Error("mbpayment_generate_sign_error", zap.String("payload", payloadStr))
		return ""
	}

	logger.Debug("mbpayment_generate_sign", zap.String("payload", payloadStr), zap.String("sign", sign))
	return sign
}
