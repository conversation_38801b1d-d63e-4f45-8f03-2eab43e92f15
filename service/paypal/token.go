package paypal

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"time"
	"x-short-server/pkg/logger"
	"x-short-server/pkg/redis"
	"x-short-server/pkg/xerr"
	"x-short-server/util"

	"github.com/valyala/fastjson"
	"go.uber.org/zap"
)

func (c *Client) GetToken() (string, error) {
	key := util.Md5(c.clientId + c.clientSecret)
	key = fmt.Sprintf(redisKeyToken, key)
	token, err := redis.Get(context.Background(), key)
	if err != nil && !xerr.IsRedisNotFound(err) {
		logger.Error("get_paypal_token_error", zap.Error(err))
	}
	if len(token) > 0 {
		return token, nil
	}

	// get token from paypal
	payload := []byte("grant_type=client_credentials")
	req, _ := http.NewRequest("POST", endpointAuthentication.Format(c.host), bytes.NewReader(payload))
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.SetBasicAuth(c.clientId, c.clientSecret)
	resp, err := c.httpclient.Do(req)
	if err != nil {
		logger.Error("get_paypal_token_error", zap.Error(err))
		return "", err
	}
	defer resp.Body.Close()
	bt, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error("read_paypal_token_error", zap.Error(err))
		return "", err
	}
	token = fastjson.GetString(bt, "access_token")
	if len(token) == 0 {
		logger.Error("decode_paypal_token_error", zap.Error(err), zap.String("body", string(bt)))
		return "", err
	}
	defer redis.Set(context.Background(), key, token, time.Minute*60)

	return token, nil
}

func (c *Client) GetTokenWithoutCache() (string, error) {
	key := util.Md5(c.clientId + c.clientSecret)
	key = fmt.Sprintf(redisKeyToken, key)
	var token string
	payload := []byte("grant_type=client_credentials")
	req, _ := http.NewRequest("POST", endpointAuthentication.Format(c.host), bytes.NewReader(payload))
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.SetBasicAuth(c.clientId, c.clientSecret)
	resp, err := c.httpclient.Do(req)
	if err != nil {
		logger.Error("get_paypal_token_error", zap.Error(err))
		return "", err
	}
	defer resp.Body.Close()
	bt, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error("read_paypal_token_error", zap.Error(err))
		return "", err
	}
	token = fastjson.GetString(bt, "access_token")
	if len(token) == 0 {
		logger.Error("decode_paypal_token_error", zap.Error(err), zap.String("body", string(bt)))
		return "", err
	}
	defer redis.Set(context.Background(), key, token, time.Minute*60)

	return token, nil
}

// GetUserToken get user token
// @return id_token, access_token, error
// HTTP request:
// POST https://api-m.sandbox.paypal.com/v1/oauth2/token
// Content-Type: application/x-www-form-urlencoded
// Authorization: Basic {{clientId}}:{{clientSecret}}
//
// grant_type=client_credentials&response_type=id_token
func (c *Client) GetUserToken() (string, error) {

	key := util.Md5(c.clientId + c.clientSecret)
	key = fmt.Sprintf(redisKeyUserToken, key)
	token, err := redis.Get(context.Background(), key)
	if err != nil && !xerr.IsRedisNotFound(err) {
		logger.Error("get_paypal_token_error", zap.Error(err))
	}
	if len(token) > 0 {
		return token, nil
	}

	payload := []byte("grant_type=client_credentials&response_type=id_token")
	req, _ := http.NewRequest("POST", endpointAuthentication.Format(c.host), bytes.NewReader(payload))
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.SetBasicAuth(c.clientId, c.clientSecret)
	req.Header.Set("PayPal-Request-Id", util.Uuid())
	resp, err := c.httpclient.Do(req)
	if err != nil {
		logger.Error("get_paypal_user_token_error", zap.Error(err))
		return "", err
	}
	defer resp.Body.Close()
	bt, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error("get_paypal_user_token_error", zap.Error(err))
		return "", err
	}
	token = fastjson.GetString(bt, "id_token")
	if len(token) == 0 {
		logger.Error("decode_paypal_user_token_error", zap.Error(err), zap.String("body", string(bt)))
		return "", err
	}

	defer redis.Set(context.Background(), key, token, time.Minute*60)

	return token, nil
}
