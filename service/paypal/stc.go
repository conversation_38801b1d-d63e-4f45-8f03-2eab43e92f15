package paypal

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"x-short-server/pkg/logger"
	"x-short-server/util"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

//  1. 初次付款中STC接口在3.2 的步骤5之前接入
//     STC API 接口链接中的Tracking-ID放需要你们步骤4中拿到的订单ID （order ID）
//     并在后续步骤5 调用capture orderAPI 的headers中加上
//     PayPal-Client-Metadata-Id = <Tracking-ID> （<Tracking-ID>为STC call中用的tracking id）
//  2. 后续扣款中STC在接口4.2 的步骤2 之前接入。此处STC 接口链接中的Tracking ID你们自定义
//     并在步骤2 的create order 的headers中加上
//     PayPal-Client-Metadata-Id = <Tracking-ID>（<Tracking-ID>为STC call中用的tracking id）
func (s *Client) PutSTC(merchantId string, trackingId string, payload gin.H) error {
	token, err := s.GetToken()
	if err != nil {
		return err
	}
	bt, err := json.Marshal(payload)
	if err != nil {
		logger.Error("paypal_put_context_error", zap.Error(err), zap.Any("payload", payload))
		return err
	}

	req, err := http.NewRequest(http.MethodPut, endpointStc.Format(s.stcHost, merchantId, trackingId), bytes.NewReader(bt))
	if err != nil {
		logger.Error("paypal_put_context_error", zap.Error(err), zap.Any("payload", payload))
		return err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("PayPal-Request-Id", util.Uuid())
	resp, err := s.httpclient.Do(req)
	if err != nil {
		logger.Error("paypal_put_context_error", zap.Error(err))
		return err
	}
	defer resp.Body.Close()
	respbt, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.WithHttpRequest(req, bt).
			WithHttpResponse(resp, respbt).
			WithError(err).
			Error("paypal_put_context_error", zap.Error(err), zap.Int("status_code", resp.StatusCode), zap.ByteString("body", bt), zap.Any("header", resp.Header))
		return err
	}
	logger.Info("paypal_put_context_response", zap.Int("status_code", resp.StatusCode), zap.String("body", string(bt)), zap.Any("header", resp.Header))
	return nil
}
