package paypal

import (
	"context"
	"crypto"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"fmt"
	"hash/crc32"
	"io"
	"x-short-server/pkg/logger"
	"x-short-server/pkg/redis"
	"x-short-server/pkg/xerr"
	"x-short-server/util"

	"errors"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

const (
	PaymentSaleCompleted         = "PAYMENT.SALE.COMPLETED"
	PaymentSaleDenied            = "PAYMENT.SALE.DENIED"
	PaymentSaleReverse           = "PAYMENT.SALE.REVERSED"
	BillingSubscriptionActivated = "BILLING.SUBSCRIPTION.ACTIVATED"
	BillingSubscriptionCancelled = "BILLING.SUBSCRIPTION.CANCELLED"

	VaultCheckoutOrderCompleted = "CHECKOUT.ORDER.COMPLETED"
	VaultCheckoutOrderReversed  = "CHECKOUT.PAYMENT-APPROVAL.REVERSED" // need capture
	VaultCheckoutOrderDenied    = "CHECKOUT.ORDER.DENIED"
	VaultCheckoutOrderCaputured = "PAYMENT.CAPTURE.COMPLETED"
	VaultPaymentTokenCreated    = "VAULT.PAYMENT-TOKEN.CREATED"
	VaultPaymentTokenDeleted    = "VAULT.PAYMENT-TOKEN.DELETED"
	VaultDisputeCreated         = "CUSTOMER.DISPUTE.CREATED"
	VaultDisputeResolved        = "CUSTOMER.DISPUTE.RESOLVED"
	VaultDisputeClosed          = "CUSTOMER.DISPUTE.CLOSED"
	VaultDisputeUpdated         = "CUSTOMER.DISPUTE.UPDATED"
)

func (p *Client) VerifySignature(c *gin.Context, webhookID string) error {
	var (
		txID            = c.GetHeader("paypal-transmission-id")
		timeStamp       = c.GetHeader("PayPal-Transmission-Time")
		signatureBase64 = c.GetHeader("PayPal-Transmission-Sig")
		certURL         = c.GetHeader("PayPal-Cert-Url")
		certPEM         []byte
		err             error
	)

	logger.Debug("Downloading PayPal cert", zap.String("url", certURL))
	certPEM, err = p.downloadAndCacheCert(certURL)
	if err != nil {
		logger.Error("download_paypal_cert_error", zap.Error(err))
		return errors.New("download_paypal_cert_error")
	}
	block, _ := pem.Decode(certPEM)
	if block == nil || block.Type != "CERTIFICATE" {
		logger.Error("failed to decode certificate")
		return errors.New("failed to decode certificate")
	}

	logger.Debug("Downloaded PayPal cert", zap.String("url", string(certPEM)))

	// Calculate CRC32 in decimal
	bodyBytes := util.CopyRawData(c)
	crc := crc32.ChecksumIEEE(bodyBytes)

	// Construct the message
	message := fmt.Sprintf("%s|%s|%s|%d", txID, timeStamp, webhookID, crc)
	logger.Debug("Original signed message", zap.String("message", message))

	// Decode base64-encoded signature
	signature, err := base64.StdEncoding.DecodeString(signatureBase64)
	if err != nil {
		logger.Error("failed to decode signature", zap.Error(err))
		return errors.New("failed to decode signature")
	}

	// Parse certificate
	cert, err := x509.ParseCertificate(block.Bytes)
	if err != nil {
		logger.Error("failed to parse certificate", zap.Error(err))
		return errors.New("failed to parse certificate")
	}

	// Get public key
	rsaPubKey, ok := cert.PublicKey.(*rsa.PublicKey)
	if !ok {
		logger.Error("failed to get public key")
		return errors.New("failed to get public key")
	}

	// Create hash
	hasher := sha256.New()
	hasher.Write([]byte(message))
	hash := hasher.Sum(nil)

	// Verify signature
	err = rsa.VerifyPKCS1v15(rsaPubKey, crypto.SHA256, hash, signature)
	if err != nil {
		logger.Error("failed to verify signature", zap.Error(err))
		return errors.New("failed to verify signature")
	}

	return nil
}

func (p *Client) downloadAndCacheCert(url string) ([]byte, error) {
	// Get cert from redis
	urlMd5 := util.Md5(url)
	cert, err := redis.Get(context.Background(), urlMd5)
	if err != nil && !xerr.IsRedisNotFound(err) {
		logger.Error("get_paypal_cert_error", zap.Error(err))
	}
	if len(cert) > 0 {
		return []byte(cert), nil
	}
	// Get cert from PayPal
	resp, err := p.httpclient.Get(url)
	if err != nil {
		logger.Error("get_paypal_cert_error", zap.Error(err))
		return nil, err
	}
	defer resp.Body.Close()
	bt, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error("read_paypal_cert_error", zap.Error(err))
		return nil, err
	}
	// Cache cert to redis
	err = redis.Set(context.Background(), urlMd5, string(bt), 0)
	if err != nil {
		logger.Error("cache_paypal_cert_error", zap.Error(err))
	}
	return bt, nil
}
