package service

import (
	"encoding/json"
)

// PC 端菜单栏
type SystemConfigMenu struct {
	Type   string `json:"type" binding:"required" form:"type"` // tag|category|video|link
	Title  string `json:"title" binding:"required" form:"title"`
	Icon   string `json:"icon,omitempty" form:"icon"`
	Target string `json:"target" binding:"required" form:"target"`
}

func SystemConfigMenuToJson(menus []SystemConfigMenu) string {
	bt, _ := json.Marshal(menus)
	return string(bt)
}

func SystemConfigMenuFromJson(j string) []SystemConfigMenu {
	var m []SystemConfigMenu
	json.Unmarshal([]byte(j), &m)
	return m
}
