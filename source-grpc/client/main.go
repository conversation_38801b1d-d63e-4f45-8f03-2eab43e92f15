package main

import (
	"context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"hello/source-grpc/pb"
	"log"
)

func main() {
	conn, err := grpc.NewClient("localhost:8093", grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		log.Fatalf("did not connect: %v", err)
	}
	defer conn.Close()
	c := pb.NewHelloServiceClient(conn)
	r, err := c.<PERSON>(context.Background(), &pb.HelloReq{Name: "jx"})
	if err != nil {
		log.Fatalf("counld not sayHello: %v", err)
	}
	log.Printf("SayHello: %s", r.<PERSON>())
}
