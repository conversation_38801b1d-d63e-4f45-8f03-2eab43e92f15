package cmd

import (
	"context"
	"encoding/gob"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"x-short-server/config"
	"x-short-server/pkg/constants"
	"x-short-server/pkg/logger"
	"x-short-server/pkg/prometheus"
	"x-short-server/router"

	"github.com/gin-contrib/sessions"
	"github.com/gin-contrib/sessions/redis"
	"github.com/gin-gonic/gin"
)

var (
	adminServer *http.Server
)

func AdminAPIServer(c context.Context) error {
	gin.DisableConsoleColor()
	if config.Cfg.Env == "production" {
		gin.SetMode(gin.ReleaseMode)
	}
	r := gin.New()

	// set session
	gob.Register(map[string]interface{}{})
	store, err := redis.NewStore(10, "tcp", config.Cfg.RedisURL, "", []byte(config.SessionSecret))
	if err != nil {
		logger.WithError(err).Panic("Failed to create redis store")
	}
	store.Options(sessions.Options{
		MaxAge: int(constants.SessionMaxAge.Seconds()),
	})
	r.Use(sessions.Sessions(constants.SessionKey, store))

	ginlogger := logger.DefaultLogger()
	r.Use(logger.GinzapWithConfig(ginlogger.Zap(), &logger.Config{
		SkipPaths:  []string{"/health", "/metrics", "/favicon.ico", "/ping", "/healthz", "/readyz", "/livez"},
		UTC:        true,
		TimeFormat: time.RFC3339,
	}), prometheus.Middleware())

	router.RegisterAdmin(r)

	adminServer = &http.Server{
		Addr:    ":" + config.Cfg.HTTPPort,
		Handler: r,
	}

	go func() {
		logger.Info("Server started")
		if err := adminServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.WithError(err).WithField("srv.addr", adminServer.Addr).Panic("Listen and serve failed")
		}
	}()
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	ctx, cancel := context.WithTimeout(c, 5*time.Second)
	defer cancel()
	GracefulShutdown(ctx, adminServer)
	return nil
}
