package cmd

import (
	"context"
	"x-short-server/pkg/logger"
)

func RefreshDataCmd(ctx context.Context, oid string) error {

	// var orders []model.PaymentOrder
	// // select po.* from payment_order po left join payment_accounts pa on po.account_id = pa.id where pa.payment_type in ('airwallex','manual_airwallex') and po.order_status in ('paid','subed') and po.sub_enabled = 1;

	// model.DB().Table("payment_order po").
	// 	Select("po.*").
	// 	Joins("left join payment_accounts pa on po.account_id = pa.id").
	// 	Where("pa.payment_type in (?) and po.order_status in (?) and po.sub_enabled = 1", []string{"airwallex", "manual_airwallex"}, []string{"paid", "subed"}).
	// 	Scan(&orders)

	// for _, order := range orders {
	// 	account, err := model.NewPaymentAccount().GetById(order.AccountId)
	// 	if account == nil || err != nil {
	// 		continue
	// 	}

	// 	client := airwallex.NewClient(account.ClientId, account.ClientSecret, *account.IsSandbox, account.Proxy)

	// 	// get last transaction id
	// 	var lastTransactionId string
	// 	model.DB().Model(&model.PaymentTransaction{}).Where("order_id = ?", order.Id).Order("created_at desc").Limit(1).Pluck("transaction_id", &lastTransactionId)

	// 	if lastTransactionId == "" {
	// 		continue
	// 	}

	// 	ord, err := client.GetPaymentIntent(lastTransactionId)
	// 	if err != nil {
	// 		continue
	// 	}

	// 	if ord.LatestPaymentAttempt.PaymentMethod.Card.ExpiryMonth == "" {
	// 		continue
	// 	}

	// 	if ord.LatestPaymentAttempt.PaymentMethod.Card.ExpiryYear == "" {
	// 		continue
	// 	}

	// 	expiryMonth := util.ParseInt(ord.LatestPaymentAttempt.PaymentMethod.Card.ExpiryMonth)
	// 	expiryYear := util.ParseInt(ord.LatestPaymentAttempt.PaymentMethod.Card.ExpiryYear)

	// 	if expiryMonth == 0 || expiryYear == 0 {
	// 		continue
	// 	}

	// 	if expiryYear < time.Now().Year() || (expiryYear == time.Now().Year() && expiryMonth < int(time.Now().Month())) {
	// 		logger.Info("支付方式已过期", zap.Any("order", order))
	// 		model.DB().Model(&model.PaymentOrder{}).Where("id = ?", order.Id).Update("sub_enabled", false)
	// 		continue
	// 	}
	// }

	logger.Info("刷新完成")
	return nil
}
