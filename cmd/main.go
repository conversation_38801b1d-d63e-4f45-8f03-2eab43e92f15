package main

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"store-dashboard-api/internal/config"
	"store-dashboard-api/internal/handler"
	"store-dashboard-api/internal/middleware"
	"store-dashboard-api/internal/model"
	"store-dashboard-api/pkg/database"
	"store-dashboard-api/pkg/logger"
)

func main() {
	// 加载配置
	if err := config.LoadConfig(); err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		os.Exit(1)
	}

	// 初始化日志
	if err := logger.InitLogger(config.AppConfig.LogLevel, config.IsProd()); err != nil {
		fmt.Printf("Failed to initialize logger: %v\n", err)
		os.Exit(1)
	}
	defer logger.Sync()

	logger.Info("Starting store-dashboard-api",
		zap.String("environment", config.AppConfig.Environment),
		zap.String("port", config.AppConfig.Port))

	// 初始化数据库
	if err := database.InitDatabase(); err != nil {
		logger.Fatal("Failed to initialize database", zap.Error(err))
	}
	defer database.CloseDatabase()

	// 数据库迁移
	if err := model.AutoMigrate(database.GetDB()); err != nil {
		logger.Fatal("Failed to migrate database", zap.Error(err))
	}

	// 创建额外索引
	//if err := model.CreateIndexes(database.GetDB()); err != nil {
	//	logger.Warn("Failed to create additional indexes", zap.Error(err))
	//}

	// 设置Gin模式
	if config.IsProd() {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建路由
	router := setupRouter()

	// 创建HTTP服务器
	srv := &http.Server{
		Addr:    ":" + config.AppConfig.Port,
		Handler: router,
	}

	// 启动服务器
	go func() {
		logger.Info("Server starting", zap.String("addr", srv.Addr))
		if err := srv.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			logger.Fatal("Failed to start server", zap.Error(err))
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down server...")

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		logger.Error("Server forced to shutdown", zap.Error(err))
	}

	logger.Info("Server exited")
}

// setupRouter 设置路由
func setupRouter() *gin.Engine {
	router := gin.New()

	// 添加中间件
	router.Use(middleware.CORS())
	router.Use(middleware.RequestLogger())
	router.Use(gin.Recovery())

	// 创建处理器
	webhookHandler := handler.NewWebhookHandler()

	// 健康检查路由
	router.GET("/health", webhookHandler.HandleHealthCheck)

	// API路由组
	api := router.Group("/api/v1")
	{
		// 测试路由
		api.POST("/test/notification", webhookHandler.HandleStoreTest)
	}

	// Webhook路由组（需要验证）
	webhook := router.Group("/webhook")
	webhook.Use(middleware.ShopifyDomainExtractor())
	webhook.Use(middleware.ShopifyWebhookAuth())
	{
		// Shopify webhook路由
		webhook.POST("/shopify/order/paid", webhookHandler.HandleOrderPaid)
	}

	return router
}
