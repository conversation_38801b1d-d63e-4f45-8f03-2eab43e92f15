package utils

import (
	"admin-backend/global"
	"bytes"
	"encoding/json"
	"io"
	"net/http"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func EzGet(client *http.Client, url string) []byte {
	resp, err := client.Get(url)
	if err != nil {
		global.Logger.Error("get_url_error", zap.Error(err))
		return nil
	}
	defer resp.Body.Close()
	bt, err := io.ReadAll(resp.Body)
	if err != nil {
		global.Logger.Error("read_body_error", zap.Error(err))
		return nil
	}
	global.Logger.Info("get_url", zap.String("url", url), zap.String("body", string(bt)))
	return bt
}

func EzPost(client *http.Client, url string, body []byte) []byte {
	resp, err := client.Post(url, "application/json", bytes.NewReader(body))
	if err != nil {
		global.Logger.Error("post_url_error", zap.Error(err))
		return nil
	}
	defer resp.Body.Close()
	bt, err := io.ReadAll(resp.Body)
	if err != nil {
		global.Logger.Error("read_body_error", zap.Error(err))
		return nil
	}
	global.Logger.Debug("post_url", zap.String("url", url), zap.String("req_body", string(body)), zap.String("resp_body", string(bt)))
	return bt
}

func CopyRawData(c *gin.Context) []byte {
	bt, err := c.GetRawData()
	if err != nil {
		global.Logger.Error("Get body failed", zap.Error(err))
		return nil
	}
	global.Logger.Debug("CopyRawData", zap.ByteString("body", bt))
	c.Request.Body = io.NopCloser(bytes.NewReader(bt))
	return bt
}

func InternalGet(client *http.Client, url string) []byte {

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		global.Logger.Error("new_request_error", zap.Error(err))
		return nil
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("X-Admin-Token", global.AdminToken)

	resp, err := client.Do(req)
	if err != nil {
		global.Logger.Error("get_url_error", zap.Error(err))
		return nil
	}
	defer resp.Body.Close()

	if resp.StatusCode == 401 {
		global.Logger.Error("get_url_error", zap.String("url", url), zap.String("status", resp.Status))
		return nil
	}

	if resp.StatusCode != 200 {
		global.Logger.Error("get_url_error", zap.String("url", url), zap.String("status", resp.Status))
		return nil
	}

	bt, err := io.ReadAll(resp.Body)
	if err != nil {
		global.Logger.Error("read_body_error", zap.Error(err))
		return nil
	}
	global.Logger.Info("get_url", zap.String("url", url), zap.String("body", string(bt)))
	return bt
}

func InternalPost(client *http.Client, url string, body any) []byte {

	var payload []byte
	// if body is byte or string
	switch body.(type) {
	case []byte:
		payload = body.([]byte)
	case string:
		payload = []byte(body.(string))
	default:
		payload, _ = json.Marshal(body)
	}

	req, err := http.NewRequest("POST", url, bytes.NewReader(payload))
	if err != nil {
		global.Logger.Error("new_request_error", zap.Error(err))
		return nil
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("X-Admin-Token", global.AdminToken)

	resp, err := client.Do(req)
	if err != nil {
		global.Logger.Error("post_url_error", zap.Error(err))
		return nil
	}
	defer resp.Body.Close()

	if resp.StatusCode == 401 {
		global.Logger.Error("post_url_error", zap.String("url", url), zap.String("status", resp.Status))
		return nil
	}

	if resp.StatusCode != 200 {
		global.Logger.Error("post_url_error", zap.String("url", url), zap.String("status", resp.Status))
		return nil
	}

	bt, err := io.ReadAll(resp.Body)
	if err != nil {
		global.Logger.Error("read_body_error", zap.Error(err))
		return nil
	}
	global.Logger.Debug("post_url", zap.String("url", url), zap.ByteString("req_body", payload), zap.ByteString("resp_body", bt))
	return bt
}
