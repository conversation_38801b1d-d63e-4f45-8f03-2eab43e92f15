package service

import (
	"fmt"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"store-dashboard-api/internal/model"
	"store-dashboard-api/pkg/database"
	"store-dashboard-api/pkg/feishu"
	"store-dashboard-api/pkg/logger"
)

// NotificationService 通知服务
type NotificationService struct {
	db           *gorm.DB
	feishuClient *feishu.Client
	orderService *OrderService
}

// NewNotificationService 创建通知服务实例
func NewNotificationService() *NotificationService {
	return &NotificationService{
		db:           database.GetDB(),
		feishuClient: feishu.NewClient(),
		orderService: NewOrderService(),
	}
}

// SendOrderNotification 发送订单通知到飞书群
func (s *NotificationService) SendOrderNotification(order *model.Order) error {
	logger.Info("Sending order notification",
		zap.Uint("order_id", order.ID),
		zap.Int64("shopify_order_id", order.ShopifyOrderID))

	// 获取店铺信息
	var store model.Store
	if err := s.db.First(&store, order.StoreID).Error; err != nil {
		logger.Error("Failed to get store for notification", zap.Error(err))
		return fmt.Errorf("failed to get store: %w", err)
	}

	// 检查店铺是否可以发送通知
	if !store.IsValidForWebhook() {
		logger.Warn("Store is not valid for webhook notification",
			zap.Uint("store_id", store.ID),
			zap.String("shop_domain", store.ShopifyDomain))
		return fmt.Errorf("store is not valid for notification")
	}

	// 获取当日统计数据
	currentDate := order.GetCurrentDateInTargetZone()
	dailyStats, err := s.orderService.GetDailyStats(store.ID, currentDate)
	if err != nil {
		logger.Error("Failed to get daily stats", zap.Error(err))
		return fmt.Errorf("failed to get daily stats: %w", err)
	}

	// 构建通知数据
	notificationData := feishu.OrderNotificationData{
		OrderID:        fmt.Sprintf("%d", order.ShopifyOrderID),
		OrderNumber:    order.OrderName,
		OrderAmount:    order.TotalPrice,
		Currency:       order.Currency,
		OrderTime:      order.GetFormattedOrderTime(),
		ReceivedTime:   order.GetFormattedReceivedTime(),
		OrderStatus:    order.FinancialStatus,
		CurrentDate:    currentDate,
		TargetTimezone: "America/Los_Angeles",
		TotalOrders:    dailyStats.TotalOrders,
		TotalGMV:       dailyStats.TotalGMV,
		CurrencyStats:  dailyStats.CurrencyStats,
		AvgOrderValue:  dailyStats.AvgOrderValue,
	}

	// 构建消息内容
	message := feishu.BuildOrderNotificationMessage(notificationData)

	// 发送飞书通知
	if err := s.feishuClient.SendTextMessage(store.FeishuWebhookURL, message); err != nil {
		logger.Error("Failed to send feishu notification",
			zap.Error(err),
			zap.Uint("order_id", order.ID),
			zap.String("feishu_webhook", store.FeishuWebhookURL))
		return fmt.Errorf("failed to send feishu notification: %w", err)
	}

	// 更新通知状态
	if err := s.markNotificationSent(order.ID); err != nil {
		logger.Warn("Failed to mark notification as sent", zap.Error(err))
	}

	logger.Info("Order notification sent successfully",
		zap.Uint("order_id", order.ID),
		zap.String("shop_domain", store.ShopifyDomain))

	return nil
}

// markNotificationSent 标记通知已发送
func (s *NotificationService) markNotificationSent(orderID uint) error {
	now := time.Now().UTC()
	return s.db.Model(&model.Order{}).
		Where("id = ?", orderID).
		Updates(map[string]interface{}{
			"notification_sent": true,
			"notification_at":   &now,
		}).Error
}

// SendTestNotification 发送测试通知
func (s *NotificationService) SendTestNotification(storeID uint) error {
	// 获取店铺信息
	var store model.Store
	if err := s.db.First(&store, storeID).Error; err != nil {
		return fmt.Errorf("failed to get store: %w", err)
	}

	if !store.IsValidForWebhook() {
		return fmt.Errorf("store is not valid for notification")
	}

	// 构建测试消息
	testMessage := fmt.Sprintf(`🧪 测试通知 - %s
📅 测试时间: %s
🏪 店铺: %s
✅ 飞书通知配置正常！`,
		store.ShopName,
		time.Now().Format("2006-01-02 15:04:05"),
		store.ShopifyDomain)

	// 发送测试通知
	return s.feishuClient.SendTextMessage(store.FeishuWebhookURL, testMessage)
}
