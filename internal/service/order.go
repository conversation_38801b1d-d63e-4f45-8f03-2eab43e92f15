package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"store-dashboard-api/internal/model"
	"store-dashboard-api/pkg/database"
	"store-dashboard-api/pkg/logger"
)

// OrderService 订单服务
type OrderService struct {
	db *gorm.DB
}

// NewOrderService 创建订单服务实例
func NewOrderService() *OrderService {
	return &OrderService{
		db: database.GetDB(),
	}
}

// ShopifyOrderWebhook Shopify订单webhook数据结构
type ShopifyOrderWebhook struct {
	ID                int64             `json:"id"`
	OrderNumber       int               `json:"order_number"`
	Name              string            `json:"name"`
	TotalPrice        string            `json:"total_price"`
	SubtotalPrice     string            `json:"subtotal_price"`
	TotalTax          string            `json:"total_tax"`
	TotalDiscounts    string            `json:"total_discounts"`
	Currency          string            `json:"currency"`
	FinancialStatus   string            `json:"financial_status"`
	FulfillmentStatus string            `json:"fulfillment_status"`
	Customer          *ShopifyCustomer  `json:"customer"`
	CreatedAt         string            `json:"created_at"`
	UpdatedAt         string            `json:"updated_at"`
	ProcessedAt       *string           `json:"processed_at"`
	BillingAddress    *ShopifyAddress   `json:"billing_address"`
	ShippingAddress   *ShopifyAddress   `json:"shipping_address"`
	LineItems         []ShopifyLineItem `json:"line_items"`
}

// ShopifyCustomer 客户信息
type ShopifyCustomer struct {
	ID    int64  `json:"id"`
	Email string `json:"email"`
	Phone string `json:"phone"`
}

// ShopifyAddress 地址信息
type ShopifyAddress struct {
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
	Address1  string `json:"address1"`
	Address2  string `json:"address2"`
	City      string `json:"city"`
	Province  string `json:"province"`
	Country   string `json:"country"`
	Zip       string `json:"zip"`
	Phone     string `json:"phone"`
}

// ShopifyLineItem 订单行项目
type ShopifyLineItem struct {
	ID       int64  `json:"id"`
	Title    string `json:"title"`
	Quantity int    `json:"quantity"`
	Price    string `json:"price"`
}

// ProcessOrderPaidWebhook 处理订单支付webhook
func (s *OrderService) ProcessOrderPaidWebhook(shopDomain string, webhookBody []byte) (orderExist bool, err error) {
	logger.Info("Processing order paid webhook", zap.String("shop_domain", shopDomain))

	// 解析webhook数据
	var shopifyOrder ShopifyOrderWebhook
	if err := json.Unmarshal(webhookBody, &shopifyOrder); err != nil {
		logger.Error("Failed to unmarshal shopify order webhook", zap.Error(err))
		return false, fmt.Errorf("failed to unmarshal webhook: %w", err)
	}

	// 查找对应的店铺
	var store model.Store
	if err := s.db.Where("shopify_domain = ? AND is_active = ?", shopDomain, true).First(&store).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Warn("Store not found or inactive", zap.String("shop_domain", shopDomain))
			return false, fmt.Errorf("store not found or inactive: %s", shopDomain)
		}
		logger.Error("Failed to query store", zap.Error(err))
		return false, fmt.Errorf("failed to query store: %w", err)
	}

	// 检查订单是否已存在
	var existingOrder model.Order
	err = s.db.Where("shopify_order_id = ?", shopifyOrder.ID).First(&existingOrder).Error
	if err == nil {
		logger.Info("Order already exists, skipping",
			zap.Int64("shopify_order_id", shopifyOrder.ID),
			zap.String("order_number", shopifyOrder.Name))
		orderExist = true
		return
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Error("Failed to check existing order", zap.Error(err))
		return false, fmt.Errorf("failed to check existing order: %w", err)
	}

	// 转换并保存订单
	order, err := s.convertShopifyOrderToModel(shopifyOrder, store.ID, webhookBody)
	if err != nil {
		logger.Error("Failed to convert shopify order", zap.Error(err))
		return false, fmt.Errorf("failed to convert order: %w", err)
	}

	// 保存订单到数据库
	if err := s.db.Create(order).Error; err != nil {
		logger.Error("Failed to save order", zap.Error(err))
		return false, fmt.Errorf("failed to save order: %w", err)
	}

	logger.Info("Order saved successfully",
		zap.Uint("order_id", order.ID),
		zap.Int64("shopify_order_id", order.ShopifyOrderID),
		zap.String("order_number", order.OrderNumber))

	// 更新店铺统计
	if err := s.updateStoreStats(store.ID); err != nil {
		logger.Warn("Failed to update store stats", zap.Error(err))
	}

	return
}

// convertShopifyOrderToModel 转换Shopify订单为数据库模型
func (s *OrderService) convertShopifyOrderToModel(shopifyOrder ShopifyOrderWebhook, storeID uint, rawData []byte) (*model.Order, error) {
	// 解析时间
	createdAt, err := time.Parse(time.RFC3339, shopifyOrder.CreatedAt)
	if err != nil {
		return nil, fmt.Errorf("failed to parse created_at: %w", err)
	}

	updatedAt, err := time.Parse(time.RFC3339, shopifyOrder.UpdatedAt)
	if err != nil {
		return nil, fmt.Errorf("failed to parse updated_at: %w", err)
	}

	// 解析金额
	totalPrice, err := parseFloat(shopifyOrder.TotalPrice)
	if err != nil {
		return nil, fmt.Errorf("failed to parse total_price: %w", err)
	}

	subtotalPrice, _ := parseFloat(shopifyOrder.SubtotalPrice)
	totalTax, _ := parseFloat(shopifyOrder.TotalTax)
	totalDiscounts, _ := parseFloat(shopifyOrder.TotalDiscounts)

	order := &model.Order{
		StoreID:           storeID,
		ShopifyOrderID:    shopifyOrder.ID,
		OrderNumber:       fmt.Sprintf("%d", shopifyOrder.OrderNumber),
		OrderName:         shopifyOrder.Name,
		TotalPrice:        totalPrice,
		SubtotalPrice:     subtotalPrice,
		TotalTax:          totalTax,
		TotalDiscounts:    totalDiscounts,
		Currency:          shopifyOrder.Currency,
		FinancialStatus:   shopifyOrder.FinancialStatus,
		FulfillmentStatus: shopifyOrder.FulfillmentStatus,
		ShopifyCreatedAt:  createdAt.UTC(),
		ShopifyUpdatedAt:  updatedAt.UTC(),
		//ReceivedAt:        time.Now().UTC(),
		RawData: rawData,
	}

	// 设置客户信息
	if shopifyOrder.Customer != nil {
		order.CustomerID = &shopifyOrder.Customer.ID
		order.CustomerEmail = shopifyOrder.Customer.Email
		order.CustomerPhone = shopifyOrder.Customer.Phone
	}

	// 设置处理时间
	if shopifyOrder.ProcessedAt != nil {
		processedAt, err := time.Parse(time.RFC3339, *shopifyOrder.ProcessedAt)
		if err == nil {
			utcTime := processedAt.UTC()
			order.ProcessedAt = &utcTime
		}
	}

	// 设置支付时间（如果已支付）
	if order.IsOrderPaid() {
		paidAt := createdAt.UTC()
		order.PaidAt = &paidAt
	}

	// 序列化地址信息
	if shopifyOrder.BillingAddress != nil {
		billingJSON, _ := json.Marshal(shopifyOrder.BillingAddress)
		order.BillingAddress = billingJSON
	}

	if shopifyOrder.ShippingAddress != nil {
		shippingJSON, _ := json.Marshal(shopifyOrder.ShippingAddress)
		order.ShippingAddress = shippingJSON
	}

	// 序列化行项目
	if len(shopifyOrder.LineItems) > 0 {
		lineItemsJSON, _ := json.Marshal(shopifyOrder.LineItems)
		order.LineItems = lineItemsJSON
	}

	return order, nil
}

// updateStoreStats 更新店铺统计信息
func (s *OrderService) updateStoreStats(storeID uint) error {
	var stats struct {
		TotalOrders int64
		TotalGMV    float64
		LastOrderAt *time.Time
	}

	// 查询统计数据
	err := s.db.Model(&model.Order{}).
		Where("store_id = ?", storeID).
		Select("COUNT(*) as total_orders, COALESCE(SUM(total_price), 0) as total_gmv, MAX(shopify_created_at) as last_order_at").
		Scan(&stats).Error

	if err != nil {
		return fmt.Errorf("failed to calculate store stats: %w", err)
	}

	// 更新店铺统计
	updateData := map[string]interface{}{
		"total_orders": stats.TotalOrders,
		"total_gmv":    stats.TotalGMV,
	}

	if stats.LastOrderAt != nil {
		updateData["last_order_at"] = stats.LastOrderAt
	}

	if err := s.db.Model(&model.Store{}).Where("id = ?", storeID).Updates(updateData).Error; err != nil {
		return fmt.Errorf("failed to update store stats: %w", err)
	}

	logger.Debug("Store stats updated",
		zap.Uint("store_id", storeID),
		zap.Int64("total_orders", stats.TotalOrders),
		zap.Float64("total_gmv", stats.TotalGMV))

	return nil
}

// GetDailyStats 获取指定店铺当日统计数据
func (s *OrderService) GetDailyStats(storeID uint, targetDate string) (*DailyStats, error) {
	// 解析目标日期
	date, err := time.Parse("2006-01-02", targetDate)
	if err != nil {
		return nil, fmt.Errorf("invalid date format: %w", err)
	}

	// 设置时区为America/Los_Angeles
	loc, _ := time.LoadLocation("America/Los_Angeles")
	startOfDay := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, loc).UTC()
	endOfDay := startOfDay.Add(24 * time.Hour)

	var stats DailyStats
	stats.Date = targetDate
	stats.CurrencyStats = make(map[string]float64)

	// 查询当日订单
	var orders []model.Order
	err = s.db.Where("store_id = ? AND shopify_created_at >= ? AND shopify_created_at < ?",
		storeID, startOfDay, endOfDay).Find(&orders).Error
	if err != nil {
		return nil, fmt.Errorf("failed to query daily orders: %w", err)
	}

	// 计算统计数据
	stats.TotalOrders = int64(len(orders))
	for _, order := range orders {
		stats.TotalGMV += order.TotalPrice
		stats.CurrencyStats[order.Currency] += order.TotalPrice
	}

	// 计算平均订单价值
	if stats.TotalOrders > 0 {
		stats.AvgOrderValue = stats.TotalGMV / float64(stats.TotalOrders)
	}

	return &stats, nil
}

// DailyStats 日统计数据
type DailyStats struct {
	Date          string             `json:"date"`
	TotalOrders   int64              `json:"total_orders"`
	TotalGMV      float64            `json:"total_gmv"`
	CurrencyStats map[string]float64 `json:"currency_stats"`
	AvgOrderValue float64            `json:"avg_order_value"`
}

// parseFloat 解析字符串为float64
func parseFloat(s string) (float64, error) {
	if s == "" {
		return 0, nil
	}

	var f float64
	_, err := fmt.Sscanf(s, "%f", &f)
	return f, err
}
