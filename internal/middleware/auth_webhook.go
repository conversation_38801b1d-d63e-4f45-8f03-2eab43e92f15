package middleware

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"io"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"store-dashboard-api/internal/config"
	"store-dashboard-api/internal/models"
	"store-dashboard-api/pkg/database"
	"store-dashboard-api/pkg/logger"
)

// ShopifyWebhookAuth Shopify Webhook验证中间件
func ShopifyWebhookAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取Shopify签名头
		signature := c.<PERSON>eader("X-Shopify-Hmac-Sha256")
		if signature == "" {
			logger.Warn("Missing Shopify HMAC signature", 
				zap.String("path", c.Request.URL.Path),
				zap.String("method", c.Request.Method))
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Missing signature"})
			c.Abort()
			return
		}

		// 读取请求体
		body, err := io.ReadAll(c.Request.Body)
		if err != nil {
			logger.Error("Failed to read request body", zap.Error(err))
			c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read request body"})
			c.Abort()
			return
		}

		// 验证签名
		if !verifyShopifyWebhook(body, signature, config.AppConfig.Shopify.WebhookSecret) {
			logger.Warn("Invalid Shopify webhook signature",
				zap.String("signature", signature),
				zap.String("path", c.Request.URL.Path))
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid signature"})
			c.Abort()
			return
		}

		// 将请求体重新设置到context中，供后续处理器使用
		c.Set("webhook_body", body)

		logger.Debug("Shopify webhook signature verified successfully",
			zap.String("path", c.Request.URL.Path),
			zap.Int("body_size", len(body)))

		c.Next()
	}
}

// verifyShopifyWebhook 验证Shopify webhook签名
func verifyShopifyWebhook(body []byte, signature, secret string) bool {
	if secret == "" {
		logger.Warn("Webhook secret not configured")
		return false
	}

	// 计算HMAC
	mac := hmac.New(sha256.New, []byte(secret))
	mac.Write(body)
	expectedMAC := mac.Sum(nil)

	// 将期望的MAC编码为base64
	expectedSignature := base64.StdEncoding.EncodeToString(expectedMAC)

	// 比较签名
	return hmac.Equal([]byte(signature), []byte(expectedSignature))
}

// ShopifyDomainExtractor 从webhook中提取Shopify域名的中间件
func ShopifyDomainExtractor() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从header中获取店铺域名
		shopDomain := c.GetHeader("X-Shopify-Shop-Domain")
		if shopDomain == "" {
			// 尝试从其他header获取
			shopDomain = c.GetHeader("X-Shopify-Domain")
		}

		if shopDomain != "" {
			// 清理域名格式，确保是 xxx.myshopify.com 格式
			shopDomain = cleanShopifyDomain(shopDomain)
			c.Set("shop_domain", shopDomain)
			
			logger.Debug("Extracted shop domain from webhook",
				zap.String("shop_domain", shopDomain))
		} else {
			logger.Warn("No shop domain found in webhook headers")
		}

		c.Next()
	}
}

// cleanShopifyDomain 清理Shopify域名格式
func cleanShopifyDomain(domain string) string {
	// 移除协议前缀
	domain = strings.TrimPrefix(domain, "https://")
	domain = strings.TrimPrefix(domain, "http://")
	
	// 移除尾部斜杠
	domain = strings.TrimSuffix(domain, "/")
	
	// 确保是.myshopify.com结尾
	if !strings.HasSuffix(domain, ".myshopify.com") {
		if !strings.Contains(domain, ".") {
			domain = domain + ".myshopify.com"
		}
	}
	
	return domain
}
