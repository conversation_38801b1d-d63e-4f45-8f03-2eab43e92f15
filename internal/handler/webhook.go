package handler

import (
	"encoding/json"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"store-dashboard-api/internal/model"
	"store-dashboard-api/internal/service"
	"store-dashboard-api/pkg/database"
	"store-dashboard-api/pkg/logger"
)

// WebhookHandler webhook处理器
type WebhookHandler struct {
	orderService        *service.OrderService
	notificationService *service.NotificationService
}

// NewWebhookHandler 创建webhook处理器实例
func NewWebhookHandler() *WebhookHandler {
	return &WebhookHandler{
		orderService:        service.NewOrderService(),
		notificationService: service.NewNotificationService(),
	}
}

// HandleOrderPaid 处理订单支付webhook
func (h *WebhookHandler) HandleOrderPaid(c *gin.Context) {
	logger.Info("Received order paid webhook",
		zap.String("path", c.Request.URL.Path),
		zap.String("method", c.Request.Method))

	// 获取店铺域名
	shopDomain, exists := c.Get("shop_domain")
	if !exists {
		logger.Error("Shop domain not found in context")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop domain not found"})
		return
	}

	// 获取webhook请求体
	webhookBody, exists := c.Get("webhook_body")
	if !exists {
		logger.Error("Webhook body not found in context")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Webhook body not found"})
		return
	}

	body, ok := webhookBody.([]byte)
	if !ok {
		logger.Error("Invalid webhook body type")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid webhook body"})
		return
	}

	domain, ok := shopDomain.(string)
	if !ok {
		logger.Error("Invalid shop domain type")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop domain"})
		return
	}

	// 处理订单webhook
	orderExist, err := h.orderService.ProcessOrderPaidWebhook(domain, body)
	if err != nil {
		logger.Error("Failed to process order paid webhook",
			zap.Error(err),
			zap.String("shop_domain", domain))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process webhook"})
		return
	}

	// 获取刚创建的订单并发送通知
	if !orderExist {
		go h.sendNotificationAsync(domain, body)
	}

	logger.Info("Order paid webhook processed successfully",
		zap.String("shop_domain", domain))

	c.JSON(http.StatusOK, gin.H{"message": "Webhook processed successfully"})
}

// sendNotificationAsync 异步发送通知
func (h *WebhookHandler) sendNotificationAsync(shopDomain string, webhookBody []byte) {
	// 这里可以添加重试逻辑和错误处理
	// 为了简化，我们直接查询最新的订单并发送通知

	// 解析webhook获取订单ID
	var webhookData struct {
		ID int64 `json:"id"`
	}

	if err := json.Unmarshal(webhookBody, &webhookData); err != nil {
		logger.Error("Failed to parse webhook for notification", zap.Error(err))
		return
	}

	// 查询订单
	var order model.Order
	if err := database.GetDB().Where("shopify_order_id = ?", webhookData.ID).First(&order).Error; err != nil {
		logger.Error("Failed to find order for notification",
			zap.Error(err),
			zap.Int64("shopify_order_id", webhookData.ID))
		return
	}

	// 发送通知
	if err := h.notificationService.SendOrderNotification(&order); err != nil {
		logger.Error("Failed to send order notification",
			zap.Error(err),
			zap.Uint("order_id", order.ID))
	}
}

// HandleHealthCheck 健康检查
func (h *WebhookHandler) HandleHealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "ok",
		"timestamp": time.Now().UTC().Format(time.RFC3339),
		"service":   "store-dashboard-api",
	})
}

// HandleStoreTest 测试店铺通知
func (h *WebhookHandler) HandleStoreTest(c *gin.Context) {
	var req struct {
		StoreID uint `json:"store_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.notificationService.SendTestNotification(req.StoreID); err != nil {
		logger.Error("Failed to send test notification",
			zap.Error(err),
			zap.Uint("store_id", req.StoreID))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to send test notification"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Test notification sent successfully"})
}
