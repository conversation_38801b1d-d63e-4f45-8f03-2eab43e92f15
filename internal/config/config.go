package config

import (
	"log"
	"os"
	"strconv"

	"github.com/joho/godotenv"
)

// Config 应用配置结构
type Config struct {
	// 环境配置
	Environment string `json:"environment"`
	Port        string `json:"port"`

	// 数据库配置
	Database DatabaseConfig `json:"database"`

	// Shopify配置
	Shopify ShopifyConfig `json:"shopify"`

	// 日志配置
	LogLevel string `json:"log_level"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
	Database string `json:"database"`
	Charset  string `json:"charset"`
}

// ShopifyConfig Shopify配置
type ShopifyConfig struct {
	ClientID     string `json:"client_id"`
	ClientSecret string `json:"client_secret"`
	AccessScope  string `json:"access_scope"`
	WebhookSecret string `json:"webhook_secret"`
}

var AppConfig *Config

// LoadConfig 加载配置
func LoadConfig() error {
	// 加载.env文件（如果存在）
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	AppConfig = &Config{
		Environment: getEnv("ENVIRONMENT", "stg"),
		Port:        getEnv("PORT", "8080"),
		LogLevel:    getEnv("LOG_LEVEL", "info"),

		Database: DatabaseConfig{
			Host:     getEnv("DB_HOST", "localhost"),
			Port:     getEnvAsInt("DB_PORT", 3306),
			Username: getEnv("DB_USERNAME", "root"),
			Password: getEnv("DB_PASSWORD", ""),
			Database: getEnv("DB_DATABASE", "store_dashboard"),
			Charset:  getEnv("DB_CHARSET", "utf8mb4"),
		},

		Shopify: ShopifyConfig{
			ClientID:     getEnv("SHOPIFY_CLIENT_ID", ""),
			ClientSecret: getEnv("SHOPIFY_CLIENT_SECRET", ""),
			AccessScope:  getEnv("SHOPIFY_ACCESS_SCOPE", "read_orders,read_products"),
			WebhookSecret: getEnv("SHOPIFY_WEBHOOK_SECRET", ""),
		},
	}

	return nil
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvAsInt 获取环境变量并转换为int，如果不存在或转换失败则返回默认值
func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// IsProd 判断是否为生产环境
func IsProd() bool {
	return AppConfig.Environment == "prod"
}

// IsStg 判断是否为测试环境
func IsStg() bool {
	return AppConfig.Environment == "stg"
}
