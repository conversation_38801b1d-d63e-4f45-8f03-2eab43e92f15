package models

import (
	"time"

	"gorm.io/gorm"
)

// Store 店铺信息表
type Store struct {
	ID        uint           `gorm:"primarykey" json:"id"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// 店铺基本信息
	ShopifyDomain string `gorm:"uniqueIndex;size:255;not null" json:"shopify_domain"`         // Shopify店铺域名，如：example.myshopify.com
	ShopName      string `gorm:"size:255;not null" json:"shop_name"`                          // 店铺名称
	ShopEmail     string `gorm:"size:255" json:"shop_email"`                                  // 店铺邮箱
	ShopCurrency  string `gorm:"size:10;default:'USD'" json:"shop_currency"`                  // 店铺货币
	ShopTimezone  string `gorm:"size:100;default:'America/Los_Angeles'" json:"shop_timezone"` // 店铺时区

	// Shopify API相关
	AccessToken   string `gorm:"size:500" json:"access_token,omitempty"`   // Shopify访问令牌（如果需要API调用）
	WebhookSecret string `gorm:"size:255" json:"webhook_secret,omitempty"` // Webhook验证密钥

	// 飞书通知配置
	FeishuWebhookURL string `gorm:"size:500;not null" json:"feishu_webhook_url"` // 飞书群机器人webhook链接

	// 状态和配置
	IsActive    bool   `gorm:"default:true" json:"is_active"` // 是否启用
	Description string `gorm:"size:500" json:"description"`   // 店铺描述

	// 统计字段（用于数据看板）
	TotalOrders int64      `gorm:"default:0" json:"total_orders"`                 // 总订单数
	TotalGMV    float64    `gorm:"type:decimal(15,2);default:0" json:"total_gmv"` // 总GMV
	LastOrderAt *time.Time `json:"last_order_at,omitempty"`                       // 最后订单时间

	// 关联关系
	Orders []Order `gorm:"foreignKey:StoreID" json:"orders,omitempty"`
}

// TableName 指定表名
func (*Store) TableName() string {
	return "stores"
}

// BeforeCreate GORM钩子：创建前
func (s *Store) BeforeCreate(tx *gorm.DB) error {
	// 设置默认时区
	if s.ShopTimezone == "" {
		s.ShopTimezone = "America/Los_Angeles"
	}

	// 设置默认货币
	if s.ShopCurrency == "" {
		s.ShopCurrency = "USD"
	}

	return nil
}

// GetTargetTimezone 获取目标时区（固定为-8时区，避免夏令时影响）
func (s *Store) GetTargetTimezone() string {
	return "America/Los_Angeles"
}

// IsValidForWebhook 检查店铺是否可以接收webhook
func (s *Store) IsValidForWebhook() bool {
	return s.IsActive && s.FeishuWebhookURL != ""
}
