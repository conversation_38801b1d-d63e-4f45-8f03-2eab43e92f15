package models

import (
	"go.uber.org/zap"
	"gorm.io/gorm"

	"store-dashboard-api/pkg/logger"
)

// AutoMigrate 自动迁移数据库表
func AutoMigrate(db *gorm.DB) error {
	logger.Info("Starting database migration...")

	// 迁移所有模型
	err := db.AutoMigrate(
		&Store{},
		&Order{},
	)

	if err != nil {
		logger.Error("Database migration failed", zap.Error(err))
		return err
	}

	logger.Info("Database migration completed successfully")
	return nil
}

// CreateIndexes 创建额外的索引
func CreateIndexes(db *gorm.DB) error {
	logger.Info("Creating additional database indexes...")

	// 为订单表创建复合索引
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_orders_store_created ON orders(store_id, shopify_created_at)").Error; err != nil {
		logger.Warn("Failed to create index idx_orders_store_created", zap.Error(err))
	}

	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_orders_store_currency ON orders(store_id, currency)").Error; err != nil {
		logger.Warn("Failed to create index idx_orders_store_currency", zap.Error(err))
	}

	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_orders_financial_status ON orders(financial_status)").Error; err != nil {
		logger.Warn("Failed to create index idx_orders_financial_status", zap.Error(err))
	}

	// 为店铺表创建索引
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_stores_active ON stores(is_active)").Error; err != nil {
		logger.Warn("Failed to create index idx_stores_active", zap.Error(err))
	}

	logger.Info("Additional database indexes created successfully")
	return nil
}
