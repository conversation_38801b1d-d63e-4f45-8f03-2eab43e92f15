package models

import (
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

// Order 订单表
type Order struct {
	ID        uint           `gorm:"primarykey" json:"id"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// 关联店铺
	StoreID uint  `gorm:"not null;index" json:"store_id"`
	Store   Store `gorm:"foreignKey:StoreID" json:"store,omitempty"`

	// Shopify订单基本信息
	ShopifyOrderID int64  `gorm:"uniqueIndex;not null" json:"shopify_order_id"` // Shopify订单ID
	OrderNumber    string `gorm:"size:100;not null;index" json:"order_number"`  // 订单号
	OrderName      string `gorm:"size:100" json:"order_name"`                   // 订单名称（如#1001）

	// 订单金额信息
	TotalPrice     float64 `gorm:"type:decimal(15,2);not null" json:"total_price"` // 订单总金额
	SubtotalPrice  float64 `gorm:"type:decimal(15,2)" json:"subtotal_price"`       // 小计金额
	TotalTax       float64 `gorm:"type:decimal(15,2)" json:"total_tax"`            // 税费
	TotalDiscounts float64 `gorm:"type:decimal(15,2)" json:"total_discounts"`      // 折扣金额
	Currency       string  `gorm:"size:10;not null" json:"currency"`               // 货币类型

	// 订单状态
	FinancialStatus   string `gorm:"size:50" json:"financial_status"`   // 财务状态
	FulfillmentStatus string `gorm:"size:50" json:"fulfillment_status"` // 履行状态

	// 客户信息
	CustomerID    *int64 `json:"customer_id,omitempty"`                // 客户ID
	CustomerEmail string `gorm:"size:255;index" json:"customer_email"` // 客户邮箱
	CustomerPhone string `gorm:"size:50" json:"customer_phone"`        // 客户电话

	// 时间信息（UTC时间存储）
	ShopifyCreatedAt time.Time  `gorm:"not null;index" json:"shopify_created_at"` // Shopify订单创建时间
	ShopifyUpdatedAt time.Time  `json:"shopify_updated_at"`                       // Shopify订单更新时间
	ProcessedAt      *time.Time `json:"processed_at,omitempty"`                   // 订单处理时间
	PaidAt           *time.Time `json:"paid_at,omitempty"`                        // 支付时间

	// 地址信息（JSON存储）
	BillingAddress  json.RawMessage `gorm:"type:json" json:"billing_address,omitempty"`  // 账单地址
	ShippingAddress json.RawMessage `gorm:"type:json" json:"shipping_address,omitempty"` // 收货地址

	// 订单行项目（JSON存储）
	LineItems json.RawMessage `gorm:"type:json" json:"line_items,omitempty"` // 订单商品列表

	// 原始数据
	RawData json.RawMessage `gorm:"type:json" json:"raw_data,omitempty"` // 原始webhook数据

	// 通知状态
	NotificationSent bool       `gorm:"default:false" json:"notification_sent"` // 是否已发送通知
	NotificationAt   *time.Time `json:"notification_at,omitempty"`              // 通知发送时间

	// 数据接收时间
	ReceivedAt time.Time `gorm:"not null;index" json:"received_at"` // 接收webhook的时间
}

// TableName 指定表名
func (*Order) TableName() string {
	return "orders"
}

// BeforeCreate GORM钩子：创建前
func (o *Order) BeforeCreate(tx *gorm.DB) error {
	if o.ReceivedAt.IsZero() {
		o.ReceivedAt = time.Now().UTC()
	}
	return nil
}

// GetTargetTimeZoneTime 获取目标时区的时间
func (o *Order) GetTargetTimeZoneTime(t time.Time) time.Time {
	// 固定使用-8时区，避免夏令时影响
	loc, _ := time.LoadLocation("America/Los_Angeles")
	return t.In(loc)
}

// GetFormattedOrderTime 获取格式化的订单时间（目标时区）
func (o *Order) GetFormattedOrderTime() string {
	targetTime := o.GetTargetTimeZoneTime(o.ShopifyCreatedAt)
	return targetTime.Format("2006-01-02 15:04:05")
}

// GetFormattedReceivedTime 获取格式化的接收时间（目标时区）
func (o *Order) GetFormattedReceivedTime() string {
	targetTime := o.GetTargetTimeZoneTime(o.ReceivedAt)
	return targetTime.Format("2006-01-02 15:04:05")
}

// GetCurrentDateInTargetZone 获取目标时区的当前日期
func (o *Order) GetCurrentDateInTargetZone() string {
	loc, _ := time.LoadLocation("America/Los_Angeles")
	now := time.Now().In(loc)
	return now.Format("2006-01-02")
}

// IsOrderPaid 检查订单是否已支付
func (o *Order) IsOrderPaid() bool {
	return o.FinancialStatus == "paid" || o.FinancialStatus == "partially_paid"
}
