# Store Dashboard API Makefile

.PHONY: help build run test clean docker-build docker-run docker-stop setup

# 默认目标
help:
	@echo "Store Dashboard API - 可用命令:"
	@echo "  build        - 编译应用"
	@echo "  run          - 运行应用"
	@echo "  test         - 运行测试"
	@echo "  clean        - 清理构建文件"
	@echo "  setup        - 初始化项目"
	@echo "  docker-build - 构建Docker镜像"
	@echo "  docker-run   - 运行Docker容器"
	@echo "  docker-stop  - 停止Docker容器"
	@echo "  deps         - 安装依赖"
	@echo "  test-webhook - 测试webhook"
	@echo "  test-multi-store - 测试多店铺webhook"

# 编译应用
build:
	@echo "编译应用..."
	go build -o main cmd/main.go
	@echo "编译完成: ./main"

# 运行应用
run:
	@echo "启动应用..."
	./scripts/start.sh

# 运行测试
test:
	@echo "运行测试..."
	go test -v ./...

# 清理构建文件
clean:
	@echo "清理构建文件..."
	rm -f main
	rm -f store-dashboard-api
	go clean

# 安装依赖
deps:
	@echo "安装Go依赖..."
	go mod tidy
	go mod download

# 初始化项目
setup:
	@echo "初始化项目..."
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "已创建.env文件，请编辑配置"; \
	fi
	@make deps
	@make build
	@echo "项目初始化完成"

# Docker相关命令
docker-build:
	@echo "构建Docker镜像..."
	docker build -t store-dashboard-api .

docker-run:
	@echo "启动Docker容器..."
	docker-compose up -d

docker-stop:
	@echo "停止Docker容器..."
	docker-compose down

docker-logs:
	@echo "查看Docker日志..."
	docker-compose logs -f

# 开发相关
dev:
	@echo "启动开发模式..."
	go run cmd/main.go

# 格式化代码
fmt:
	@echo "格式化代码..."
	go fmt ./...

# 代码检查
lint:
	@echo "代码检查..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "golangci-lint未安装，跳过代码检查"; \
	fi

# 生成文档
docs:
	@echo "生成API文档..."
	@if command -v swag >/dev/null 2>&1; then \
		swag init -g cmd/main.go; \
	else \
		echo "swag未安装，跳过文档生成"; \
	fi

# 数据库迁移
migrate:
	@echo "运行数据库迁移..."
	@if [ -f scripts/setup_database.sql ]; then \
		echo "请手动运行数据库脚本: scripts/setup_database.sql"; \
	fi

# 测试webhook
test-webhook:
	@echo "测试webhook..."
	./scripts/test_webhook.sh

# 测试多店铺webhook
test-multi-store:
	@echo "测试多店铺webhook..."
	./scripts/test_multi_store.sh

# 安装开发工具
install-tools:
	@echo "安装开发工具..."
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	go install github.com/swaggo/swag/cmd/swag@latest

# 版本信息
version:
	@echo "Go版本: $(shell go version)"
	@echo "项目: Store Dashboard API"
	@echo "构建时间: $(shell date)"
