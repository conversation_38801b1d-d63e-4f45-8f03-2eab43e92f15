# 部署指南

## 本地开发环境

### 1. 环境准备

- Go 1.21+
- MySQL 5.7+
- Git

### 2. 克隆项目

```bash
git clone <repository-url>
cd store-dashboard-api
```

### 3. 配置环境

```bash
# 复制环境变量文件
cp .env.example .env

# 编辑配置
vim .env
```

### 4. 初始化项目

```bash
# 使用Makefile快速初始化
make setup

# 或手动执行
make deps
make build
```

### 5. 准备数据库

```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE store_dashboard CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 运行应用（自动迁移）
make run
```

### 6. 配置店铺信息

```sql
-- 连接数据库
mysql -u root -p store_dashboard

-- 插入店铺信息
INSERT INTO stores (
    shopify_domain, 
    shop_name, 
    shop_email,
    shop_currency, 
    feishu_webhook_url, 
    is_active,
    description
) VALUES (
    'your-store.myshopify.com',
    '你的店铺名称',
    '<EMAIL>',
    'USD',
    'https://open.feishu.cn/open-apis/bot/v2/hook/your-webhook-url',
    true,
    '店铺描述'
);
```

## Docker部署

### 1. 使用Docker Compose（推荐）

```bash
# 编辑docker-compose.yml中的环境变量
vim docker-compose.yml

# 启动服务
make docker-run

# 查看日志
make docker-logs

# 停止服务
make docker-stop
```

### 2. 单独构建Docker镜像

```bash
# 构建镜像
make docker-build

# 运行容器
docker run -d \
  --name store-dashboard-api \
  -p 8080:8080 \
  -e ENVIRONMENT=prod \
  -e DB_HOST=your-db-host \
  -e DB_PASSWORD=your-db-password \
  -e SHOPIFY_WEBHOOK_SECRET=your-secret \
  store-dashboard-api
```

## 生产环境部署

### 1. 服务器准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要软件
sudo apt install -y git mysql-server nginx certbot

# 安装Go
wget https://go.dev/dl/go1.21.0.linux-amd64.tar.gz
sudo tar -C /usr/local -xzf go1.21.0.linux-amd64.tar.gz
echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
source ~/.bashrc
```

### 2. 部署应用

```bash
# 创建应用目录
sudo mkdir -p /opt/store-dashboard-api
sudo chown $USER:$USER /opt/store-dashboard-api

# 克隆代码
cd /opt/store-dashboard-api
git clone <repository-url> .

# 配置环境变量
cp .env.example .env
vim .env  # 编辑生产环境配置

# 编译应用
make build
```

### 3. 配置系统服务

创建systemd服务文件：

```bash
sudo vim /etc/systemd/system/store-dashboard-api.service
```

内容：
```ini
[Unit]
Description=Store Dashboard API
After=network.target mysql.service

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/opt/store-dashboard-api
ExecStart=/opt/store-dashboard-api/main
Restart=always
RestartSec=5
Environment=ENVIRONMENT=prod

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable store-dashboard-api
sudo systemctl start store-dashboard-api
sudo systemctl status store-dashboard-api
```

### 4. 配置Nginx反向代理

```bash
sudo vim /etc/nginx/sites-available/store-dashboard-api
```

内容：
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 特殊处理webhook路径
    location /webhook/ {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 保持原始请求体
        proxy_buffering off;
        proxy_request_buffering off;
    }
}
```

启用站点：
```bash
sudo ln -s /etc/nginx/sites-available/store-dashboard-api /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 5. 配置SSL证书

```bash
# 使用Let's Encrypt
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加：0 12 * * * /usr/bin/certbot renew --quiet
```

## Shopify配置

### 1. 创建Webhook

在Shopify后台：
1. 进入 Settings > Notifications
2. 滚动到 Webhooks 部分
3. 点击 "Create webhook"
4. 配置：
   - Event: `Order payment`
   - Format: `JSON`
   - URL: `https://your-domain.com/webhook/shopify/order/paid`

### 2. 配置验证密钥

在webhook设置中：
1. 设置一个强密码作为验证密钥
2. 将密钥添加到环境变量 `SHOPIFY_WEBHOOK_SECRET`
3. 重启应用

## 飞书机器人配置

### 1. 创建飞书群机器人

1. 在飞书群中添加机器人
2. 选择"自定义机器人"
3. 配置机器人名称和头像
4. 获取Webhook URL
5. 将URL保存到数据库的店铺配置中

### 2. 测试通知

```bash
# 使用API测试
curl -X POST http://your-domain.com/api/v1/test/notification \
  -H "Content-Type: application/json" \
  -d '{"store_id": 1}'
```

## 监控和日志

### 1. 查看应用日志

```bash
# systemd日志
sudo journalctl -u store-dashboard-api -f

# 应用日志（如果配置了文件输出）
tail -f /var/log/store-dashboard-api.log
```

### 2. 健康检查

```bash
# 检查服务状态
curl http://localhost:8080/health

# 检查数据库连接
mysql -u root -p -e "SELECT COUNT(*) FROM store_dashboard.stores;"
```

### 3. 性能监控

可以集成以下监控工具：
- Prometheus + Grafana
- ELK Stack (Elasticsearch, Logstash, Kibana)
- 云监控服务

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接参数
   - 检查防火墙设置

2. **Webhook签名验证失败**
   - 确认SHOPIFY_WEBHOOK_SECRET配置正确
   - 检查Shopify后台webhook配置

3. **飞书通知发送失败**
   - 验证飞书webhook URL
   - 检查网络连接
   - 确认机器人权限

### 日志级别

在生产环境建议使用：
```bash
LOG_LEVEL=warn  # 或 error
```

在开发环境可以使用：
```bash
LOG_LEVEL=debug  # 或 info
```

## 备份和恢复

### 数据库备份

```bash
# 备份
mysqldump -u root -p store_dashboard > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复
mysql -u root -p store_dashboard < backup_20250621_120000.sql
```

### 应用备份

```bash
# 备份应用目录
tar -czf store-dashboard-api-backup-$(date +%Y%m%d).tar.gz /opt/store-dashboard-api
```
