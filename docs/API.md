# Store Dashboard API 接口文档

## 概述

Store Dashboard API 是一个用于监控Shopify店铺订单的后端服务，支持webhook接收、数据存储和飞书通知。

## 基础信息

- **Base URL**: `http://localhost:8080`
- **Content-Type**: `application/json`
- **时区**: UTC (数据库存储) / America/Los_Angeles (显示)

## 接口列表

### 1. 健康检查

检查服务状态。

**请求**
```
GET /health
```

**响应**
```json
{
  "status": "ok",
  "timestamp": "2025-06-21T15:30:00Z",
  "service": "store-dashboard-api"
}
```

### 2. Shopify订单支付Webhook

接收Shopify的订单支付事件。

**请求**
```
POST /webhook/shopify/order/paid
Content-Type: application/json
X-Shopify-Hmac-Sha256: {signature}
X-Shopify-Shop-Domain: {shop_domain}
```

**请求体**
```json
{
  "id": 12345678901,
  "order_number": 1001,
  "name": "#1001",
  "total_price": "99.99",
  "currency": "USD",
  "financial_status": "paid",
  "customer": {
    "id": 987654321,
    "email": "<EMAIL>"
  },
  "created_at": "2025-06-21T15:30:00Z",
  "line_items": [
    {
      "id": 123456789,
      "title": "Product Name",
      "quantity": 1,
      "price": "99.99"
    }
  ]
}
```

**响应**
```json
{
  "message": "Webhook processed successfully"
}
```

**错误响应**
```json
{
  "error": "Invalid signature"
}
```

### 3. 测试飞书通知

发送测试通知到指定店铺的飞书群。

**请求**
```
POST /api/v1/test/notification
Content-Type: application/json
```

**请求体**
```json
{
  "store_id": 1
}
```

**响应**
```json
{
  "message": "Test notification sent successfully"
}
```

**错误响应**
```json
{
  "error": "Store not found or invalid"
}
```

## 错误码

| HTTP状态码 | 说明 |
|-----------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 签名验证失败 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## Webhook签名验证

Shopify webhook使用HMAC-SHA256进行签名验证，支持多店铺不同密钥：

### 验证流程
1. 从webhook header中提取店铺域名
2. 查询数据库获取该店铺的专用webhook secret
3. 如果店铺没有专用secret，使用全局`SHOPIFY_WEBHOOK_SECRET`
4. 使用获取的密钥对请求体进行HMAC-SHA256计算
5. 将结果进行Base64编码
6. 与`X-Shopify-Hmac-Sha256`头部比较

### 密钥优先级
1. **店铺专用密钥**：数据库中`stores.webhook_secret`字段
2. **全局密钥**：环境变量`SHOPIFY_WEBHOOK_SECRET`

## 飞书通知格式

新订单通知包含以下信息：

- 订单基本信息（ID、金额、状态等）
- 时间信息（订单时间、接收时间）
- 当日统计数据（总订单数、GMV、平均订单价值）
- 按货币分组的统计

示例通知：
```
🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉
🆕 检测到新订单! (2025-06-21)
🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉
📦 订单ID: 12345678901
🔢 订单号: #1001
💰 订单金额: $99.99
📅 订单时间: 2025-06-21 07:30:00
📧 邮件接收时间: 2025-06-21 07:30:00
✅ 订单状态: paid

📊📊📊📊📊📊📊📊📊📊
📈 累积GMV统计 (2025-06-21)
🌏 目标时区: America/Los_Angeles
📅 当前日期: 2025-06-21 (America/Los_Angeles)
📊📊📊📊📊📊📊📊📊📊
📦 总订单数: 5
💵 总GMV: 499.95

按货币统计:
  💰 $: 499.95

📊 平均订单价值: 99.99
📊📊📊📊📊📊📊📊📊📊
```

## 数据库配置

在使用前需要在数据库中配置店铺信息：

### 使用店铺专用webhook secret
```sql
INSERT INTO stores (
    shopify_domain,
    shop_name,
    shop_currency,
    webhook_secret,
    feishu_webhook_url,
    is_active
) VALUES (
    'your-store.myshopify.com',
    '你的店铺名称',
    'USD',
    'your_store_specific_webhook_secret',
    'https://open.feishu.cn/open-apis/bot/v2/hook/your-webhook-url',
    true
);
```

### 使用全局webhook secret
```sql
INSERT INTO stores (
    shopify_domain,
    shop_name,
    shop_currency,
    webhook_secret,
    feishu_webhook_url,
    is_active
) VALUES (
    'your-store.myshopify.com',
    '你的店铺名称',
    'USD',
    NULL,  -- 使用全局webhook secret
    'https://open.feishu.cn/open-apis/bot/v2/hook/your-webhook-url',
    true
);
```

## 测试

使用提供的测试脚本：

```bash
# 测试基本功能
./scripts/test_webhook.sh

# 带签名验证的测试
WEBHOOK_SECRET=your_secret ./scripts/test_webhook.sh
```
