# renewal_history 数据修复工具

## 背景

renewal_history 表是在 2024年5月16日 才建立的，而项目从 2024年3月 就开始运行了，因此 renewal_history 表缺少了很多历史数据。

## 修复思路

根据 payment_transactions 表的数据进行恢复，通过以下 SQL 找出支付过的订单 ID 及支付次数：

```sql
SELECT po.id as order_id, COUNT(*) as count
FROM payment_order po
LEFT JOIN payment_transactions pt ON po.id = pt.order_id
WHERE po.order_status IN ('paid', 'subed', 'cancel', 'refund')
GROUP BY po.id;
```

其中 `count` 为首次订阅+续订的支付成功次数。

## 修复规则

### cnt=1 (仅首订)
- 可能有1次续费失败
- **条件**: `period_end_time` 小于当前时间，且订单状态不是 `cancel` 或 `refund`
- **生成记录**:
  - renewal_times = 1
  - created_at、updated_at = order.period_end_time
  - status = failed

### cnt=2 (1次续费成功)
- 可能有1次失败
- **生成记录1** (续费成功):
  - renewal_times = 1
  - created_at、updated_at = order.start_time + period天数
  - status = success
- **生成记录2** (如果期限已过且不是cancel/refund状态):
  - renewal_times = 2
  - created_at、updated_at = order.period_end_time
  - status = failed

### cnt=3 (2次续费成功)
- **生成记录1**:
  - renewal_times = 1
  - created_at、updated_at = order.start_time + period天数
  - status = success
- **生成记录2**:
  - renewal_times = 2
  - created_at、updated_at = order.start_time + period天数*2
  - status = success

### cnt>3 (多次续费成功)
- 生成 `cnt-1` 条成功记录
- 每条记录的时间为 `order.start_time + period天数*续费次数`

## 安全机制

1. **避免重复插入**: 按 `(order_id, renewal_times)` 唯一性检查，如果记录已存在则跳过
2. **预览模式**: 支持 `--dry_run` 参数，仅预览修复结果，不实际执行
3. **详细日志**: 记录关键操作和修复结果统计
4. **进度显示**: 每处理100个订单输出一次进度

## 使用方法

### 预览模式（推荐先运行）
```bash
./x-short-server fix_renewal_history --dry_run
```

### 实际执行修复
```bash
./x-short-server fix_renewal_history
```

### 查看帮助
```bash
./x-short-server fix_renewal_history --help
```

## 输出示例

### 预览模式输出
```
{"level":"info","msg":"开始修复renewal_history数据","dry_run":true}
{"level":"info","msg":"查询到订单数量","total_orders":1500}
{"level":"info","msg":"预览模式：将插入记录","order_id":12345,"renewal_times":1,"status":"success","created_at":"2024-04-01T00:00:00Z"}
{"level":"info","msg":"处理进度","processed":100,"total":1500,"inserted":85,"skipped":15,"errors":0}
...
{"level":"info","msg":"修复完成","total_orders":1500,"processed_count":1500,"inserted_count":850,"skipped_count":650,"error_count":0,"dry_run":true}
```

### 实际执行输出
```
{"level":"info","msg":"开始修复renewal_history数据","dry_run":false}
{"level":"info","msg":"查询到订单数量","total_orders":1500}
{"level":"debug","msg":"插入记录成功","order_id":12345,"renewal_times":1,"status":"success"}
{"level":"info","msg":"处理进度","processed":100,"total":1500,"inserted":85,"skipped":15,"errors":0}
...
{"level":"info","msg":"修复完成","total_orders":1500,"processed_count":1500,"inserted_count":850,"skipped_count":650,"error_count":0,"dry_run":false}
```

## 统计说明

- **total_orders**: 需要处理的订单总数
- **processed_count**: 已处理的订单数
- **inserted_count**: 实际插入的记录数
- **skipped_count**: 跳过的记录数（已存在或不符合条件）
- **error_count**: 处理过程中的错误数

## 注意事项

1. **建议先运行预览模式**，确认修复结果符合预期
2. **在生产环境运行前**，建议在测试环境先验证
3. **数据库备份**：执行前建议备份 renewal_history 表
4. **监控资源**：大量数据处理时注意数据库连接和内存使用
5. **幂等性**：可以多次运行，已存在的记录会被跳过

## 技术实现

- 使用 GORM 进行数据库操作
- 支持事务处理确保数据一致性
- 使用雪花算法生成唯一ID
- 详细的错误处理和日志记录
- 内存友好的批量处理方式
