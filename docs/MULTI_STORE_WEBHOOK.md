# 多店铺Webhook配置指南

## 概述

本系统支持多个Shopify店铺的webhook接入，每个店铺可以配置独立的webhook验证密钥，提供更好的安全性和灵活性。

## Webhook密钥验证机制

### 验证流程

1. **提取店铺域名**：从webhook请求头`X-Shopify-Shop-Domain`中获取店铺域名
2. **查询店铺配置**：根据域名查询数据库中的店铺信息
3. **获取验证密钥**：按优先级获取webhook验证密钥
4. **签名验证**：使用HMAC-SHA256算法验证webhook签名

### 密钥优先级

系统按以下优先级选择webhook验证密钥：

1. **店铺专用密钥**（最高优先级）
   - 数据库字段：`stores.webhook_secret`
   - 适用场景：每个店铺使用不同的webhook密钥

2. **全局密钥**（备用选项）
   - 环境变量：`SHOPIFY_WEBHOOK_SECRET`
   - 适用场景：所有店铺使用相同的webhook密钥

## 配置方式

### 方式1：店铺专用密钥（推荐）

#### 优势
- 更高的安全性：每个店铺独立的密钥
- 更好的隔离性：一个店铺的密钥泄露不影响其他店铺
- 灵活管理：可以单独更换某个店铺的密钥

#### 配置步骤

1. **在Shopify后台配置webhook**
   ```
   店铺A: webhook密钥设置为 "store_a_secret_key"
   店铺B: webhook密钥设置为 "store_b_secret_key"
   ```

2. **在数据库中配置店铺信息**
   ```sql
   -- 店铺A
   INSERT INTO stores (
       shopify_domain, 
       shop_name, 
       webhook_secret,
       feishu_webhook_url, 
       is_active
   ) VALUES (
       'store-a.myshopify.com',
       '店铺A',
       'store_a_secret_key',
       'https://open.feishu.cn/open-apis/bot/v2/hook/webhook-url-a',
       true
   );

   -- 店铺B
   INSERT INTO stores (
       shopify_domain, 
       shop_name, 
       webhook_secret,
       feishu_webhook_url, 
       is_active
   ) VALUES (
       'store-b.myshopify.com',
       '店铺B',
       'store_b_secret_key',
       'https://open.feishu.cn/open-apis/bot/v2/hook/webhook-url-b',
       true
   );
   ```

### 方式2：全局密钥

#### 优势
- 配置简单：只需要设置一个环境变量
- 管理方便：所有店铺使用相同的密钥

#### 配置步骤

1. **设置环境变量**
   ```bash
   SHOPIFY_WEBHOOK_SECRET=global_webhook_secret
   ```

2. **在数据库中配置店铺信息**
   ```sql
   INSERT INTO stores (
       shopify_domain, 
       shop_name, 
       webhook_secret,  -- 设置为NULL使用全局密钥
       feishu_webhook_url, 
       is_active
   ) VALUES (
       'store-a.myshopify.com',
       '店铺A',
       NULL,
       'https://open.feishu.cn/open-apis/bot/v2/hook/webhook-url-a',
       true
   );
   ```

### 方式3：混合配置

可以同时使用两种方式：部分店铺使用专用密钥，部分店铺使用全局密钥。

```sql
-- 店铺A使用专用密钥
INSERT INTO stores (shopify_domain, webhook_secret, ...) 
VALUES ('store-a.myshopify.com', 'store_a_secret', ...);

-- 店铺B使用全局密钥
INSERT INTO stores (shopify_domain, webhook_secret, ...) 
VALUES ('store-b.myshopify.com', NULL, ...);
```

## 安全建议

### 1. 密钥管理
- 使用强密码生成器生成webhook密钥
- 定期更换webhook密钥
- 不要在代码中硬编码密钥

### 2. 密钥存储
- 数据库中的密钥字段应该加密存储（可选）
- 限制数据库访问权限
- 定期备份数据库

### 3. 监控和日志
- 监控webhook验证失败的情况
- 记录异常的webhook请求
- 设置告警机制

## 故障排除

### 常见问题

1. **签名验证失败**
   ```
   错误信息：Invalid Shopify webhook signature
   ```
   **解决方案**：
   - 检查店铺域名是否正确配置
   - 验证webhook密钥是否匹配
   - 确认Shopify后台webhook配置

2. **店铺未找到**
   ```
   错误信息：Store not found or webhook secret not configured
   ```
   **解决方案**：
   - 检查数据库中是否存在对应的店铺记录
   - 确认店铺状态为active
   - 验证shopify_domain字段格式

3. **全局密钥未配置**
   ```
   错误信息：Webhook secret not configured
   ```
   **解决方案**：
   - 设置环境变量SHOPIFY_WEBHOOK_SECRET
   - 或在数据库中为店铺配置专用密钥

### 调试步骤

1. **检查日志**
   ```bash
   # 查看应用日志
   tail -f /var/log/store-dashboard-api.log
   
   # 或使用systemd日志
   journalctl -u store-dashboard-api -f
   ```

2. **验证店铺配置**
   ```sql
   SELECT shopify_domain, webhook_secret, is_active 
   FROM stores 
   WHERE shopify_domain = 'your-store.myshopify.com';
   ```

3. **测试webhook**
   ```bash
   # 使用测试脚本
   WEBHOOK_SECRET=your_secret ./scripts/test_webhook.sh
   ```

## 最佳实践

1. **推荐使用店铺专用密钥**：提供更好的安全性
2. **定期轮换密钥**：建议每3-6个月更换一次
3. **监控webhook状态**：设置告警监控webhook失败情况
4. **备份配置**：定期备份数据库中的店铺配置
5. **文档记录**：记录每个店铺的webhook配置信息

## 迁移指南

### 从全局密钥迁移到店铺专用密钥

1. **准备阶段**
   - 为每个店铺生成新的webhook密钥
   - 准备数据库更新脚本

2. **执行迁移**
   ```sql
   -- 更新店铺A的密钥
   UPDATE stores 
   SET webhook_secret = 'new_store_a_secret' 
   WHERE shopify_domain = 'store-a.myshopify.com';
   ```

3. **更新Shopify配置**
   - 在Shopify后台更新webhook密钥
   - 测试webhook是否正常工作

4. **验证迁移**
   - 发送测试订单验证webhook
   - 检查日志确认无错误

通过以上配置，您可以灵活地管理多个Shopify店铺的webhook验证，确保系统的安全性和可靠性。
