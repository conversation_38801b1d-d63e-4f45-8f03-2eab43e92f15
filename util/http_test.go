package util

import (
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
)

var testctx = &gin.Context{
	Request: &http.Request{
		Header: map[string][]string{
			"Referer": {"https://rapidxreels.com/videos/25865534042025984?token=2uFeIrEX4wzP6fSuzQR3ihJWBMQ&utm_medium=paid&utm_source=fb&utm_id=12021836"},
		},
	},
}

func TestGetUtmMedium(t *testing.T) {
	if got := GetUtmMedium(testctx); got != "paid" {
		t.Errorf("GetMedium() = %v, want %v", got, "paid")
	}
}

func TestGetUtmSource(t *testing.T) {
	if got := GetUtmSource(testctx); got != "fb" {
		t.Errorf("GetSource() = %v, want %v", got, "fb")
	}
}

func TestGetUtmId(t *testing.T) {
	if got := GetUtmId(testctx); got != "12021836" {
		t.<PERSON><PERSON><PERSON>("GetId() = %v, want %v", got, "12021836")
	}
}
