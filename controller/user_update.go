package controller

import (
	"x-short-server/request"
	"x-short-server/response"
	"x-short-server/service"

	"github.com/gin-gonic/gin"
)

func UpdateUser(c *gin.Context) {
	var (
		form    request.UserRegisterForm
		userSvc = service.NewUserService()
	)

	if err := c.ShouldBindJSON(&form); err != nil {
		response.BadRequest(c, "Invalid request")
		return
	}

	token := userSvc.GetUserToken(c)
	if token == "" {
		response.Unauthorized(c, "Unauthorized")
		return
	}

	user, err := userSvc.GetUserFromToken(c, token)
	if err != nil {
		response.Unauthorized(c, "Unauthorized")
		return
	}

	if err := userSvc.UpdateUserBySelf(c, user, form); err != nil {
		if err == service.ErrEmailAlreadyExists {
			response.BadRequest(c, "Email already exists")
			return
		}
		response.InternalServerError(c, "Can't update user")
		return
	}

	response.OK(c, response.NewUserInfoResponseFromModel(user))
}
