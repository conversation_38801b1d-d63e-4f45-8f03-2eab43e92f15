package manage

import (
	"x-short-server/model"
	"x-short-server/pkg/logger"
	"x-short-server/request"
	response "x-short-server/response"
	"x-short-server/util"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func CreateRefererToken(c *gin.Context) {
	var req request.AdminReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request")
		return
	}
	aid := util.ParseUint64(req.AdminId)
	token, err := model.NewAdminRefererTokenModel().Create(aid)
	if err != nil {
		response.InternalServerError(c, "Can't create referer token")
		return
	}
	response.OK(c, token)
}

func ListRefererToken(c *gin.Context) {
	var req struct {
		request.Pagination
		request.AdminReq
	}
	if err := c.ShouldBind(&req); err != nil {
		response.BadRequest(c, "Invalid request")
		return
	}
	tokens, err := model.NewAdminRefererTokenModel().List(req.Page, req.PerPage, "", util.ParseUint64(req.AdminId))
	if err != nil {
		logger.Error("cannot_list_referer_token", zap.Error(err), zap.Any("req", req))
		response.InternalServerError(c, "Can't list referer token")
		return
	}
	response.OK(c, tokens)
}

func DeleteRefererToken(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		response.BadRequest(c, "Invalid request")
		return
	}
	var (
		uid uint64 = util.ParseUint64(id)
		req request.AdminReq
	)
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request")
		return
	}
	aid := util.ParseUint64(req.AdminId)
	err := model.NewAdminRefererTokenModel().DeleteById(uid, aid)
	if err != nil {
		logger.Error("cannot_delete_referer_token", zap.Error(err), zap.String("id", id))
		response.InternalServerError(c, "Can't delete referer token")
		return
	}
	response.OK(c, nil)
}
