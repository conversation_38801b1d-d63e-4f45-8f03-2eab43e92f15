package manage

import (
	"x-short-server/model"
	"x-short-server/request"
	"x-short-server/response"
	"x-short-server/util"

	"github.com/gin-gonic/gin"
)

func ListPaymentAccount(c *gin.Context) {

	var (
		err         error
		arr         []model.PaymentAccount
		withDeleted bool = false
	)

	if c.Query("with_deleted") == "1" {
		withDeleted = true
	}

	arr, err = paymentSvc.ListPayment(c, withDeleted)
	if err != nil {
		response.InternalServerError(c, "获取支付账号列表失败: "+err.Error())
		return
	}

	if len(arr) == 0 {
		arr = make([]model.PaymentAccount, 0)
	}

	response.OK(c, gin.H{"list": arr})
}

var supportPaymentAccountType = util.NewStringSet(
	model.PaymentAccountTypeAirwallex,
	model.PaymentAccountTypePayPal,
	model.PaymentAccountTypeVaultPayPal,
	model.PaymentAccountTypeCardPayPal,
	model.PaymentAccountTypeManualAirwallex,
)

func CreatePaymentAccount(c *gin.Context) {

	req := request.CreatePaymentAccountForm{}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	if !supportPaymentAccountType.Contains(req.PaymentType) {
		response.BadRequest(c, "不支持的支付类型")
		return
	}

	if req.PaymentType == model.PaymentAccountTypeAirwallex || req.PaymentType == model.PaymentAccountTypeManualAirwallex {
		if req.ClientId == "" || req.ClientSecret == "" {
			response.BadRequest(c, "apiKey和clientId不能为空")
			return
		}
	}

	if req.PaymentType == model.PaymentAccountTypePayPal {
		if req.ClientId == "" || req.ClientSecret == "" {
			response.BadRequest(c, "clientId和clientSecret不能为空")
			return
		}
	}

	var (
		err error
	)

	m := &model.PaymentAccount{
		Website:      req.Website,
		Name:         req.Name,
		PaymentType:  req.PaymentType,
		ClientId:     req.ClientId,
		ClientSecret: req.ClientSecret,
		IsSandbox:    util.Bool(req.IsSandbox == 1),
		Enabled:      util.Bool(req.Enabled == 1),
		Account:      req.Account,
		MerchantId:   req.MerchantId,
	}

	m, err = paymentSvc.CreatePayment(c, m)
	if err != nil {
		response.InternalServerError(c, "创建支付账号失败: "+err.Error())
		return
	}

	response.OK(c, *m)
}

func UpdatePaymentAccount(c *gin.Context) {

	var (
		id  string = c.Param("id")
		uid uint64 = util.ParseUint64(id)
	)

	req := request.UpdatePaymentAccountForm{}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	var (
		enabled *bool
		err     error
		exist   *model.PaymentAccount
	)

	if req.Enabled == 1 {
		enabled = util.Bool(true)
	} else {
		enabled = util.Bool(false)
	}

	exist, err = model.NewPaymentAccount().GetById(uid)
	if err != nil || exist == nil {
		response.InternalServerError(c, "账号不存在: "+err.Error())
		return
	}

	exist.Name = req.Name
	exist.Enabled = enabled
	exist.Account = req.Account
	exist.MerchantId = req.MerchantId
	exist.WebhookSecret = req.WebhookSecret

	exist, err = paymentSvc.UpdatePayment(c, exist)
	if err != nil {
		response.InternalServerError(c, "更新支付账号失败: "+err.Error())
		return
	}

	response.OK(c, *exist)
}

func DeletePaymentAccount(c *gin.Context) {

	var (
		id  string = c.Param("id")
		uid uint64 = util.ParseUint64(id)
		err error
	)

	if uid == 0 {
		response.BadRequest(c, "参数错误")
		return
	}

	err = paymentSvc.Delete(c, uid)
	if err != nil {
		response.InternalServerError(c, "删除支付账号失败: "+err.Error())
		return
	}

	response.OK(c, nil)
}
