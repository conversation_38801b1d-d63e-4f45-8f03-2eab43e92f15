#!/bin/bash

# 多店铺webhook测试脚本

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 多店铺Webhook测试脚本 ===${NC}"

# 设置变量
API_URL="http://localhost:8080"
WEBHOOK_URL="$API_URL/webhook/shopify/order/paid"

# 店铺配置数组
declare -A STORES
STORES["store-a.myshopify.com"]="store_a_secret_key"
STORES["store-b.myshopify.com"]="store_b_secret_key"
STORES["store-c.myshopify.com"]=""  # 使用全局密钥

# 全局密钥
GLOBAL_SECRET="global_webhook_secret"

# 模拟订单数据模板
get_webhook_data() {
    local order_id=$1
    local store_name=$2
    
    cat <<EOF
{
  "id": ${order_id},
  "order_number": 100${order_id},
  "name": "#100${order_id}",
  "total_price": "99.99",
  "subtotal_price": "89.99",
  "total_tax": "10.00",
  "total_discounts": "0.00",
  "currency": "USD",
  "financial_status": "paid",
  "fulfillment_status": "unfulfilled",
  "customer": {
    "id": *********,
    "email": "customer@${store_name}",
    "phone": "+*********0"
  },
  "created_at": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "updated_at": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "processed_at": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "billing_address": {
    "first_name": "John",
    "last_name": "Doe",
    "address1": "123 Main St",
    "city": "New York",
    "province": "NY",
    "country": "United States",
    "zip": "10001"
  },
  "line_items": [
    {
      "id": *********,
      "title": "Test Product from ${store_name}",
      "quantity": 1,
      "price": "89.99"
    }
  ]
}
EOF
}

# 测试单个店铺webhook
test_store_webhook() {
    local shop_domain=$1
    local webhook_secret=$2
    local order_id=$3
    
    echo -e "\n${YELLOW}测试店铺: $shop_domain${NC}"
    
    # 生成订单数据
    local webhook_data=$(get_webhook_data $order_id $shop_domain)
    
    # 确定使用的密钥
    local secret_to_use
    if [ -n "$webhook_secret" ]; then
        secret_to_use="$webhook_secret"
        echo -e "${YELLOW}使用店铺专用密钥: ${webhook_secret:0:8}...${NC}"
    else
        secret_to_use="$GLOBAL_SECRET"
        echo -e "${YELLOW}使用全局密钥: ${GLOBAL_SECRET:0:8}...${NC}"
    fi
    
    # 计算签名
    local signature=$(echo -n "$webhook_data" | openssl dgst -sha256 -hmac "$secret_to_use" -binary | base64)
    
    # 发送请求
    local response=$(curl -s -w "%{http_code}" \
        -H "Content-Type: application/json" \
        -H "X-Shopify-Hmac-Sha256: $signature" \
        -H "X-Shopify-Shop-Domain: $shop_domain" \
        -d "$webhook_data" \
        -o /tmp/webhook_response_${shop_domain}.json \
        "$WEBHOOK_URL")
    
    local http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✓ 店铺 $shop_domain webhook测试成功${NC}"
        if command -v jq &> /dev/null; then
            cat /tmp/webhook_response_${shop_domain}.json | jq .
        else
            cat /tmp/webhook_response_${shop_domain}.json
        fi
    else
        echo -e "${RED}✗ 店铺 $shop_domain webhook测试失败 (HTTP $http_code)${NC}"
        cat /tmp/webhook_response_${shop_domain}.json
    fi
}

# 检查API是否运行
echo -e "\n${YELLOW}1. 检查API服务状态...${NC}"
health_response=$(curl -s -w "%{http_code}" -o /tmp/health_response.json "$API_URL/health")
health_code="${health_response: -3}"

if [ "$health_code" = "200" ]; then
    echo -e "${GREEN}✓ API服务正常运行${NC}"
else
    echo -e "${RED}✗ API服务未运行或异常 (HTTP $health_code)${NC}"
    echo -e "${RED}请先启动API服务: make run${NC}"
    exit 1
fi

# 测试所有店铺
echo -e "\n${YELLOW}2. 开始测试多店铺webhook...${NC}"

order_counter=1
for shop_domain in "${!STORES[@]}"; do
    webhook_secret="${STORES[$shop_domain]}"
    test_store_webhook "$shop_domain" "$webhook_secret" "$order_counter"
    ((order_counter++))
    sleep 1  # 避免请求过快
done

# 测试错误情况
echo -e "\n${YELLOW}3. 测试错误情况...${NC}"

# 测试无效签名
echo -e "\n${YELLOW}3.1 测试无效签名...${NC}"
invalid_signature="invalid_signature_base64"
webhook_data=$(get_webhook_data 9999 "invalid-test")

response=$(curl -s -w "%{http_code}" \
    -H "Content-Type: application/json" \
    -H "X-Shopify-Hmac-Sha256: $invalid_signature" \
    -H "X-Shopify-Shop-Domain: store-a.myshopify.com" \
    -d "$webhook_data" \
    -o /tmp/invalid_signature_response.json \
    "$WEBHOOK_URL")

http_code="${response: -3}"
if [ "$http_code" = "401" ]; then
    echo -e "${GREEN}✓ 无效签名正确被拒绝${NC}"
else
    echo -e "${RED}✗ 无效签名测试失败，期望401，实际$http_code${NC}"
fi

# 测试未知店铺
echo -e "\n${YELLOW}3.2 测试未知店铺...${NC}"
unknown_signature=$(echo -n "$webhook_data" | openssl dgst -sha256 -hmac "$GLOBAL_SECRET" -binary | base64)

response=$(curl -s -w "%{http_code}" \
    -H "Content-Type: application/json" \
    -H "X-Shopify-Hmac-Sha256: $unknown_signature" \
    -H "X-Shopify-Shop-Domain: unknown-store.myshopify.com" \
    -d "$webhook_data" \
    -o /tmp/unknown_store_response.json \
    "$WEBHOOK_URL")

http_code="${response: -3}"
if [ "$http_code" = "401" ]; then
    echo -e "${GREEN}✓ 未知店铺正确被拒绝${NC}"
else
    echo -e "${RED}✗ 未知店铺测试失败，期望401，实际$http_code${NC}"
fi

# 清理临时文件
echo -e "\n${YELLOW}4. 清理临时文件...${NC}"
rm -f /tmp/webhook_response_*.json /tmp/health_response.json /tmp/invalid_signature_response.json /tmp/unknown_store_response.json

echo -e "\n${BLUE}=== 多店铺测试完成 ===${NC}"
echo -e "${YELLOW}注意: 此测试需要数据库中已配置对应的店铺信息${NC}"
echo -e "${YELLOW}请参考 scripts/setup_database.sql 配置测试店铺${NC}"
