#!/bin/bash

# 快速启动脚本

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Store Dashboard API 启动脚本 ===${NC}"

# 检查.env文件
if [ ! -f ".env" ]; then
    echo -e "${YELLOW}警告: .env文件不存在，正在从.env.example创建...${NC}"
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo -e "${GREEN}✓ 已创建.env文件，请编辑配置后重新运行${NC}"
        echo -e "${YELLOW}请编辑.env文件中的数据库和Shopify配置${NC}"
        exit 1
    else
        echo -e "${RED}✗ .env.example文件不存在${NC}"
        exit 1
    fi
fi

# 加载环境变量
source .env

echo -e "${YELLOW}当前配置:${NC}"
echo -e "  环境: ${ENVIRONMENT:-stg}"
echo -e "  端口: ${PORT:-8080}"
echo -e "  数据库: ${DB_HOST:-localhost}:${DB_PORT:-3306}/${DB_DATABASE:-store_dashboard}"

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo -e "${RED}✗ Go未安装或不在PATH中${NC}"
    exit 1
fi

echo -e "${GREEN}✓ Go环境检查通过${NC}"

# 检查依赖
echo -e "${YELLOW}检查Go依赖...${NC}"
go mod tidy
if [ $? -ne 0 ]; then
    echo -e "${RED}✗ Go依赖安装失败${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Go依赖检查通过${NC}"

# 编译应用
echo -e "${YELLOW}编译应用...${NC}"
go build -o main cmd/main.go
if [ $? -ne 0 ]; then
    echo -e "${RED}✗ 应用编译失败${NC}"
    exit 1
fi
echo -e "${GREEN}✓ 应用编译成功${NC}"

# 检查数据库连接（可选）
if command -v mysql &> /dev/null && [ ! -z "$DB_PASSWORD" ]; then
    echo -e "${YELLOW}测试数据库连接...${NC}"
    mysql -h"${DB_HOST:-localhost}" -P"${DB_PORT:-3306}" -u"${DB_USERNAME:-root}" -p"$DB_PASSWORD" -e "SELECT 1;" 2>/dev/null
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ 数据库连接成功${NC}"
    else
        echo -e "${YELLOW}⚠ 数据库连接失败，请检查配置${NC}"
    fi
fi

# 启动应用
echo -e "\n${BLUE}启动应用...${NC}"
echo -e "${YELLOW}访问地址: http://localhost:${PORT:-8080}${NC}"
echo -e "${YELLOW}健康检查: http://localhost:${PORT:-8080}/health${NC}"
echo -e "${YELLOW}按 Ctrl+C 停止服务${NC}\n"

# 启动应用
./main
