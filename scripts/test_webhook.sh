#!/bin/bash

# 测试webhook脚本

# 设置变量
API_URL="http://localhost:8080"
WEBHOOK_URL="$API_URL/webhook/shopify/order/paid"
HEALTH_URL="$API_URL/health"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== Store Dashboard API 测试脚本 ===${NC}"

# 1. 测试健康检查
echo -e "\n${YELLOW}1. 测试健康检查...${NC}"
response=$(curl -s -w "%{http_code}" -o /tmp/health_response.json "$HEALTH_URL")
http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    echo -e "${GREEN}✓ 健康检查通过${NC}"
    cat /tmp/health_response.json | jq .
else
    echo -e "${RED}✗ 健康检查失败 (HTTP $http_code)${NC}"
    cat /tmp/health_response.json
fi

# 2. 测试Shopify webhook（模拟数据）
echo -e "\n${YELLOW}2. 测试Shopify webhook...${NC}"

# 模拟Shopify订单数据
webhook_data='{
  "id": *********01,
  "order_number": 1001,
  "name": "#1001",
  "total_price": "99.99",
  "subtotal_price": "89.99",
  "total_tax": "10.00",
  "total_discounts": "0.00",
  "currency": "USD",
  "financial_status": "paid",
  "fulfillment_status": "unfulfilled",
  "customer": {
    "id": *********,
    "email": "<EMAIL>",
    "phone": "+*********0"
  },
  "created_at": "2025-06-21T15:30:00Z",
  "updated_at": "2025-06-21T15:30:00Z",
  "processed_at": "2025-06-21T15:30:00Z",
  "billing_address": {
    "first_name": "John",
    "last_name": "Doe",
    "address1": "123 Main St",
    "city": "New York",
    "province": "NY",
    "country": "United States",
    "zip": "10001"
  },
  "line_items": [
    {
      "id": *********,
      "title": "Test Product",
      "quantity": 1,
      "price": "89.99"
    }
  ]
}'

# 计算HMAC签名（需要设置WEBHOOK_SECRET）
if [ -z "$WEBHOOK_SECRET" ]; then
    echo -e "${YELLOW}警告: 未设置WEBHOOK_SECRET环境变量，跳过签名验证测试${NC}"
    echo -e "${YELLOW}使用方法: WEBHOOK_SECRET=your_secret $0${NC}"
else
    # 计算签名
    signature=$(echo -n "$webhook_data" | openssl dgst -sha256 -hmac "$WEBHOOK_SECRET" -binary | base64)
    
    echo -e "${YELLOW}发送webhook请求...${NC}"
    response=$(curl -s -w "%{http_code}" \
        -H "Content-Type: application/json" \
        -H "X-Shopify-Hmac-Sha256: $signature" \
        -H "X-Shopify-Shop-Domain: test-store.myshopify.com" \
        -d "$webhook_data" \
        -o /tmp/webhook_response.json \
        "$WEBHOOK_URL")
    
    http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✓ Webhook请求成功${NC}"
        cat /tmp/webhook_response.json | jq .
    else
        echo -e "${RED}✗ Webhook请求失败 (HTTP $http_code)${NC}"
        cat /tmp/webhook_response.json
    fi
fi

# 3. 测试通知接口
echo -e "\n${YELLOW}3. 测试通知接口...${NC}"
test_notification_data='{"store_id": 1}'

response=$(curl -s -w "%{http_code}" \
    -H "Content-Type: application/json" \
    -d "$test_notification_data" \
    -o /tmp/notification_response.json \
    "$API_URL/api/v1/test/notification")

http_code="${response: -3}"

if [ "$http_code" = "200" ]; then
    echo -e "${GREEN}✓ 测试通知请求成功${NC}"
    cat /tmp/notification_response.json | jq .
else
    echo -e "${RED}✗ 测试通知请求失败 (HTTP $http_code)${NC}"
    cat /tmp/notification_response.json
fi

echo -e "\n${YELLOW}=== 测试完成 ===${NC}"

# 清理临时文件
rm -f /tmp/health_response.json /tmp/webhook_response.json /tmp/notification_response.json
