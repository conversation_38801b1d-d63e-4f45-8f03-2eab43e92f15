-- 数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS store_dashboard CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE store_dashboard;

-- 示例店铺数据插入脚本
-- 请根据实际情况修改以下数据

-- 示例店铺1
INSERT INTO stores (
    shopify_domain, 
    shop_name, 
    shop_email, 
    shop_currency, 
    shop_timezone, 
    feishu_webhook_url, 
    is_active, 
    description,
    created_at,
    updated_at
) VALUES (
    'example-store-1.myshopify.com',
    '示例店铺1',
    '<EMAIL>',
    'USD',
    'America/Los_Angeles',
    'https://open.feishu.cn/open-apis/bot/v2/hook/your-webhook-url-1',
    true,
    '这是第一个示例店铺',
    NOW(),
    NOW()
) ON DUPLICATE KEY UPDATE
    shop_name = VALUES(shop_name),
    updated_at = NOW();

-- 示例店铺2
INSERT INTO stores (
    shopify_domain, 
    shop_name, 
    shop_email, 
    shop_currency, 
    shop_timezone, 
    feishu_webhook_url, 
    is_active, 
    description,
    created_at,
    updated_at
) VALUES (
    'example-store-2.myshopify.com',
    '示例店铺2',
    '<EMAIL>',
    'EUR',
    'America/Los_Angeles',
    'https://open.feishu.cn/open-apis/bot/v2/hook/your-webhook-url-2',
    true,
    '这是第二个示例店铺',
    NOW(),
    NOW()
) ON DUPLICATE KEY UPDATE
    shop_name = VALUES(shop_name),
    updated_at = NOW();

-- 查看插入的店铺数据
SELECT 
    id,
    shopify_domain,
    shop_name,
    shop_currency,
    is_active,
    created_at
FROM stores 
WHERE is_active = true;

-- 显示表结构
DESCRIBE stores;
DESCRIBE orders;
