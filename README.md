# Store Dashboard API

基于Golang的Shopify店铺订单监控系统，通过webhook监听新订单支付事件，存储订单数据并发送飞书通知。

## 功能特性

- 🛒 **多店铺支持**: 支持接入多个Shopify店铺的订单数据
- 📊 **实时监控**: 通过Shopify webhook实时监听订单支付事件
- 💬 **飞书通知**: 新订单自动发送到对应店铺的飞书群
- 📈 **数据统计**: 提供当日GMV统计和订单分析
- 🗄️ **数据存储**: 使用MySQL存储订单和店铺数据
- 🔒 **安全验证**: Shopify webhook签名验证
- 🌍 **时区处理**: 支持America/Los_Angeles时区（-8时区）

## 技术栈

- **后端框架**: Gin (Golang)
- **数据库**: MySQL + GORM
- **日志**: Zap
- **配置**: 环境变量
- **通知**: 飞书机器人

## 项目结构

```
store-dashboard-api/
├── cmd/                    # 应用入口
│   └── main.go
├── internal/              # 内部包
│   ├── config/           # 配置管理
│   ├── models/           # 数据模型
│   ├── handlers/         # HTTP处理器
│   ├── services/         # 业务逻辑
│   └── middleware/       # 中间件
├── pkg/                  # 可导出包
│   ├── logger/          # 日志
│   ├── database/        # 数据库
│   └── feishu/          # 飞书通知
├── .env.example         # 环境变量示例
├── go.mod
└── README.md
```

## 快速开始

### 1. 环境准备

- Go 1.21+
- MySQL 5.7+

### 2. 配置环境变量

```bash
cp .env.example .env
# 编辑.env文件，填入你的配置
```

### 3. 数据库准备

创建MySQL数据库：

```sql
CREATE DATABASE store_dashboard CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 4. 安装依赖

```bash
go mod tidy
```

### 5. 运行应用

```bash
go run cmd/main.go
```

## 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| ENVIRONMENT | 环境(stg/prod) | stg |
| PORT | 服务端口 | 8080 |
| LOG_LEVEL | 日志级别 | info |
| DB_HOST | 数据库主机 | localhost |
| DB_PORT | 数据库端口 | 3306 |
| DB_USERNAME | 数据库用户名 | root |
| DB_PASSWORD | 数据库密码 | - |
| DB_DATABASE | 数据库名 | store_dashboard |
| SHOPIFY_WEBHOOK_SECRET | Webhook验证密钥 | - |

### 店铺配置

在数据库的`stores`表中手动添加店铺信息。支持两种webhook secret配置方式：

#### 方式1：使用店铺专用webhook secret（推荐）
```sql
INSERT INTO stores (shopify_domain, shop_name, shop_currency, webhook_secret, feishu_webhook_url, is_active)
VALUES ('your-store.myshopify.com', '店铺名称', 'USD', 'store_specific_secret', 'https://open.feishu.cn/open-apis/bot/v2/hook/xxx', true);
```

#### 方式2：使用全局webhook secret
```sql
INSERT INTO stores (shopify_domain, shop_name, shop_currency, webhook_secret, feishu_webhook_url, is_active)
VALUES ('your-store.myshopify.com', '店铺名称', 'USD', NULL, 'https://open.feishu.cn/open-apis/bot/v2/hook/xxx', true);
```

## API接口

### Webhook接口

- `POST /webhook/shopify/order/paid` - Shopify订单支付webhook

### 管理接口

- `GET /health` - 健康检查
- `POST /api/v1/test/notification` - 测试飞书通知

## Shopify配置

### 1. 创建Webhook

在Shopify后台 Settings > Notifications > Webhooks 中创建webhook：

- **Event**: Order payment
- **Format**: JSON
- **URL**: `https://your-domain.com/webhook/shopify/order/paid`

### 2. 设置验证密钥

支持两种配置方式：

#### 全局密钥（适用于所有店铺）
在环境变量中设置：`SHOPIFY_WEBHOOK_SECRET=your_global_secret`

#### 店铺专用密钥（推荐）
在Shopify后台为每个店铺设置不同的webhook密钥，然后在数据库中配置：
```sql
UPDATE stores SET webhook_secret = 'store_specific_secret' WHERE shopify_domain = 'your-store.myshopify.com';
```

**密钥优先级**：店铺专用密钥 > 全局密钥

## 飞书通知格式

```
🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉
🆕 检测到新订单! (2025-06-20)
🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉
📦 订单ID: 12345
🔢 订单号: #1001
💰 订单金额: $99.99
📅 订单时间: 2025-06-20 07:03:39
📧 邮件接收时间: 2025-06-20 07:03:39
✅ 订单状态: paid

📊📊📊📊📊📊📊📊📊📊
📈 累积GMV统计 (2025-06-20)
🌏 目标时区: America/Los_Angeles
📅 当前日期: 2025-06-20 (America/Los_Angeles)
📊📊📊📊📊📊📊📊📊📊
📦 总订单数: 10
💵 总GMV: 999.90

按货币统计:
  💰 $: 999.90

📊 平均订单价值: 99.99
📊📊📊📊📊📊📊📊📊📊
```

## 数据库表结构

### stores表 - 店铺信息

| 字段 | 类型 | 说明 |
|------|------|------|
| id | uint | 主键 |
| shopify_domain | string | Shopify域名 |
| shop_name | string | 店铺名称 |
| webhook_secret | string | 店铺专用webhook密钥（可选） |
| feishu_webhook_url | string | 飞书群机器人链接 |
| is_active | bool | 是否启用 |

### orders表 - 订单信息

| 字段 | 类型 | 说明 |
|------|------|------|
| id | uint | 主键 |
| store_id | uint | 店铺ID |
| shopify_order_id | int64 | Shopify订单ID |
| order_number | string | 订单号 |
| total_price | decimal | 订单金额 |
| currency | string | 货币类型 |
| shopify_created_at | datetime | 订单创建时间(UTC) |

## 部署

### Docker部署

```dockerfile
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go mod tidy && go build -o main cmd/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/
COPY --from=builder /app/main .
CMD ["./main"]
```

### 系统服务

创建systemd服务文件 `/etc/systemd/system/store-dashboard-api.service`：

```ini
[Unit]
Description=Store Dashboard API
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/opt/store-dashboard-api
ExecStart=/opt/store-dashboard-api/main
Restart=always

[Install]
WantedBy=multi-user.target
```

## 开发计划

- [ ] 数据看板Web界面
- [ ] 订单详情查询API
- [ ] 多时区支持
- [ ] 订单状态变更通知
- [ ] 数据导出功能

## 许可证

MIT License