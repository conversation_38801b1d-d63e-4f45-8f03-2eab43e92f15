package config

type MysqlConfig struct {
	MysqlUsername string `env:"MYSQL_USERNAME" envDefault:"root"`
	MysqlPassword string `env:"MYSQL_PASSWORD" envDefault:"root"`
	MysqlHostname string `env:"MYSQL_HOSTNAME" envDefault:""`
	MysqlPort     int    `env:"MYSQL_PORT" envDefault:"3306"`
	MysqlDatabase string `env:"MYSQL_DATABASE" envDefault:"ola"`
	MysqlTimezone string `env:"MYSQL_TIMEZONE" envDefault:"Asia/Shanghai"`
	MysqlCharset  string `env:"MYSQL_CHARSET" envDefault:"utf8mb4"`
}
