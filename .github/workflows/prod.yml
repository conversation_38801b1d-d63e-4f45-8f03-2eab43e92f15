name: prod-deploy

on:
  workflow_dispatch:
    inputs:
      tag:
        description: 'Tag'
        default: ''
        required: true

env:
  LARK_URL: ${{ vars.DEPLOY_NOTIFY_URL }}
  IMAGE_ID: x-short-server
  IMAGE_TAG: ${{ github.run_id }}
  ECR_HOST: ${{ vars.ECR_HOST }}
  IMAGE: ${{ vars.ECR_HOST }}/x-short-server:${{ github.run_id }}
  TAG: ${{ github.event.inputs.tag }}

jobs:
  build:
    runs-on: stg
    steps:
      - name: Checkout
        run: |
          if [ -d ${IMAGE_ID} ]; then rm -rf ${IMAGE_ID}; fi
          git clone --branch=$TAG https://$GITHUB_ACTOR_ID:${{ secrets.GITHUB_TOKEN }}@github.com/$GITHUB_REPOSITORY.git ${IMAGE_ID}
      - name: Build
        run: |
          cd ${IMAGE_ID}
          aws ecr get-login-password --region us-west-1 | sudo docker login --username AWS --password-stdin ${ECR_HOST}
          aws ecr describe-repositories --region us-west-1 --repository-name $IMAGE_ID || aws ecr create-repository --region us-west-1 --repository-name $IMAGE_ID
          sudo docker build -t $IMAGE_ID:$IMAGE_TAG .
          sudo docker tag $IMAGE_ID:$IMAGE_TAG $IMAGE
          sudo docker push $IMAGE
          echo "Build image $IMAGE_ID:$IMAGE_TAG successfully!"
  deploy:
    runs-on: prod
    needs: build
    steps:
      - name: Checkout
        run: |
          if [ -d ${IMAGE_ID} ]; then rm -rf ${IMAGE_ID}; fi
          git clone --branch=$TAG https://$GITHUB_ACTOR_ID:${{ secrets.GITHUB_TOKEN }}@github.com/$GITHUB_REPOSITORY.git ${IMAGE_ID}
      - name: Env Check
        run: |
          if [ ! -d ~/env ]; then
            echo "env not found!"
            exit 1
          fi
          if [ ! -f ~/env/x-short-server.env ]; then
            echo "env/x-short-server.env not found!"
            exit 1
          fi
      - name: Migration
        run: |
          cd ${IMAGE_ID}
          aws ecr get-login-password --region us-west-1 | sudo docker login --username AWS --password-stdin ${ECR_HOST}
          sudo docker pull $IMAGE
          sudo docker run --name ${IMAGE_ID}-migration-${GITHUB_RUN_ID} --rm --env-file ~/env/x-short-server.env ${IMAGE} /main migration
      - name: Deploy
        run: |
          function MESG() {
            content=$1
            TEXT="[PROD][$IMAGE_ID]($GITHUB_SERVER_URL/$GITHUB_REPOSITORY)\nMesg: ${content}\nTime: $(date +'%Y-%m-%d %H:%M:%S') \nCommit: $COMMIT_URL\nAction: $ACTION_URL"
            TEXT=$(echo $TEXT | sed 's/"/\\"/g')
            curl -X POST -H "Content-Type: application/json" -d "{\"msg_type\":\"text\",\"content\":{\"text\":\"$TEXT\"}}" $LARK_URL
          }

          # 使用 docker compose ls 判断 prod1 是否在运行
          RUNNING=$(sudo docker compose ls | grep prod1 | awk '{print $1}')
          if [ -z $RUNNING ]; then
            RUNNING=prod2
          fi
          INSTALL_FLAG=prod1  # 要部署的服务
          INSTALL_PODNAME=x-short-api-1 # 要部署的服务名
          INSTALL_PORT1=8088  # 要部署的服务端口
          INSTALL_PORT2=8089  # 要部署的服务端口
          UNINSTALL_FLAG=prod2 # 要卸载的服务
          UNINSTALL_PORT1=9088 # 要卸载的服务端口
          UNINSTALL_PORT2=9089 # 要卸载的服务端口
          if [ $INSTALL_FLAG == $RUNNING ]; then
            INSTALL_FLAG=prod2
            INSTALL_PODNAME=x-short-api-2
            INSTALL_PORT1=9088
            INSTALL_PORT2=9089
            UNINSTALL_FLAG=prod1
            UNINSTALL_PORT1=8088
            UNINSTALL_PORT2=8089
          fi

          # 预部署预版本
          cd ${IMAGE_ID}/deploy/${INSTALL_FLAG}
          COMMIT_URL=$GITHUB_SERVER_URL/$GITHUB_REPOSITORY/commit/$(git rev-parse --short HEAD)
          ACTION_URL=$GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID
          sed -i "s|\${IMAGE}|$IMAGE|g" docker-compose.yml
          aws ecr get-login-password --region us-west-1 | sudo docker login --username AWS --password-stdin ${ECR_HOST}
          sudo docker compose up -d && \
          MESG "PRODUCTION ENV deployed successfully!\nINSTALL_FLAG=${INSTALL_FLAG}" || \
          (MESG "PRODUCTION ENV deployed failed!" && exit 1)

          # 检查健康状态
          timeout=60
          while [ $timeout -gt 0 ]; do
            HEALTHY=$(sudo docker inspect --format='{{.State.Health.Status}}' $INSTALL_PODNAME)
            if [ $HEALTHY == "healthy" ]; then
              break
            fi
            sleep 10
            timeout=$((timeout-10))
          done
          if [ $HEALTHY != "healthy" ]; then
            MESG "PRODUCTION ENV health check failed!\nINSTALL_FLAG=${INSTALL_FLAG}" && \
            exit 1
          fi

          # 修改 nginx 配置文件
          sudo sed -i "s/localhost:$UNINSTALL_PORT1/localhost:$INSTALL_PORT1/g" /etc/nginx/sites-enabled/default
          sudo sed -i "s/localhost:$UNINSTALL_PORT2/localhost:$INSTALL_PORT2/g" /etc/nginx/sites-enabled/default
          sudo sed -i "s/localhost:$UNINSTALL_PORT1/localhost:$INSTALL_PORT1/g" /etc/nginx/snippets/common.conf
          sudo sed -i "s/localhost:$UNINSTALL_PORT2/localhost:$INSTALL_PORT2/g" /etc/nginx/snippets/common.conf
          sudo nginx -t || ( MESG "PRODUCTION ENV nginx config test failed!\nINSTALL_FLAG=${INSTALL_FLAG}" && exit 1 )
          sudo nginx -s reload || ( MESG "PRODUCTION ENV nginx reload failed!\nINSTALL_FLAG=${INSTALL_FLAG}" && exit 1 )

          # 清理旧版本
          cd ../../deploy/${UNINSTALL_FLAG}
          sed -i "s|\${IMAGE}|$IMAGE|g" docker-compose.yml
          sudo docker compose down || ( MESG "PRODUCTION ENV uninstall failed!\nUNINSTALL_FLAG=${UNINSTALL_FLAG}" && exit 1 )
          MESG "PRODUCTION ENV uninstall successfully!\nUNINSTALL_FLAG=${UNINSTALL_FLAG}"

      - name: Clean
        run: |
          if [ $(df -h | grep /dev/root | awk '{print $4}' | sed 's/G//') -lt 10 ]; then
            sudo docker system prune -f && \
            curl -X POST -H "Content-Type: application/json" -d "{\"msg_type\":\"text\",\"content\":{\"text\":\"[PROD][$IMAGE_ID]($GITHUB_SERVER_URL/$GITHUB_REPOSITORY)\nMesg: Disk space is not enough! Cleaned successfully! \nTime: $(date +'%Y-%m-%d %H:%M:%S')\"}}" $LARK_URL
          else
            echo "Disk space is enough! Skip clean!"
          fi
