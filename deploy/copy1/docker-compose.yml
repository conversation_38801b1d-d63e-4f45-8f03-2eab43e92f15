version: "3"
services:
  api:
    image: ${IMAGE}
    container_name: x-short-api-1
    restart: always
    command: ["sh", "-c", "/main webserver"]
    ports:
      - "8088:80"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/ping"]
      interval: 10s
      retries: 5
      start_period: 30s
      timeout: 10s
    env_file:
      - path: /home/<USER>/env/x-short-server.env
        required: true
    logging:
      driver: loki
      options:
        loki-url: "http://************:3100/loki/api/v1/push"
        loki-external-labels: "environment=production,app=x-short-api"
  adminapi:
    image: ${IMAGE}
    container_name: x-short-adminapi-1
    restart: always
    command: ["sh", "-c", "/main adminserver"]
    ports:
      - "8089:80"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/ping"]
      interval: 10s
      retries: 5
      start_period: 30s
      timeout: 10s
    env_file:
      - path: /home/<USER>/env/x-short-server.env
        required: true
    logging:
      driver: loki
      options:
        loki-url: "http://************:3100/loki/api/v1/push"
        loki-external-labels: "environment=production,app=x-short-adminapi"
  paymentqueue:
    image: ${IMAGE}
    container_name: x-short-paymentqueue-1
    restart: always
    command: ["sh", "-c", "/main paymentqueue"]
    ports:
      - "8090:80"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/metrics"]
      interval: 10s
      retries: 5
      start_period: 30s
      timeout: 10s
    env_file:
      - path: /home/<USER>/env/x-short-server.env
        required: true
    logging:
      driver: loki
      options:
        loki-url: "http://************:3100/loki/api/v1/push"
        loki-external-labels: "environment=production,app=x-short-paymentqueue"
  cronjob:
    image: ${IMAGE}
    container_name: x-short-cronjob-1
    restart: always
    command: ["sh", "-c", "/main cronjob"]
    ports:
      - "8091:80"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/metrics"]
      interval: 10s
      retries: 5
      start_period: 30s
      timeout: 10s
    env_file:
      - path: /home/<USER>/env/x-short-server.env
        required: true
    logging:
      driver: loki
      options:
        loki-url: "http://************:3100/loki/api/v1/push"
        loki-external-labels: "environment=production,app=x-short-cronjob"
  pixelqueue:
    image: ${IMAGE}
    container_name: x-short-pixelqueue-1
    restart: always
    command: ["sh", "-c", "/main pixelqueue"]
    ports:
      - "8092:80"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/metrics"]
      interval: 10s
      retries: 5
      start_period: 30s
      timeout: 10s
    env_file:
      - path: /home/<USER>/env/x-short-server.env
        required: true
    logging:
      driver: loki
      options:
        loki-url: "http://************:3100/loki/api/v1/push"
        loki-external-labels: "environment=production,app=x-short-pixelqueue"
