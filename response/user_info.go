package response

import (
	"time"
	"x-short-server/model"
	"x-short-server/util"
)

type UserInfoResponse struct {
	ID          string `json:"id"`
	Email       string `json:"email"`
	Username    string `json:"username"`
	Avatar      string `json:"avatar"`
	IsAdvance   bool   `json:"is_advance"`
	Language    string `json:"language"`
	IsAnonymous bool   `json:"is_anonymous"`
	Locked      bool   `json:"locked"` // 被封号
	ExpiredAt   string `json:"expired_at"`
	UserType    string `json:"user_type"`
	DramaLimit  int64  `json:"drama_limit"`
	VIP         bool   `json:"vip"`
}

func NewUserInfoResponseFromModel(user *model.User) *UserInfoResponse {
	expired := ""
	vip := false
	if user.SubscriptionExpireAt != nil {
		expired = user.SubscriptionExpireAt.Format(time.RFC3339)
		if time.Now().Before(*user.SubscriptionExpireAt) {
			vip = true
		}
	}
	return &UserInfoResponse{
		ID:          util.FormatUint64(user.Id),
		Email:       user.Email,
		Username:    user.Username,
		Avatar:      user.Avatar,
		IsAdvance:   user.IsAdvanceUser(),
		Language:    user.Language,
		IsAnonymous: user.IsAnonymousUser(),
		Locked:      user.LockedAt != nil && !user.LockedAt.IsZero() && time.Now().After(*user.LockedAt),
		ExpiredAt:   expired,
		UserType:    user.UserType,
		DramaLimit:  user.UnlockDrama,
		VIP:         vip,
	}
}
