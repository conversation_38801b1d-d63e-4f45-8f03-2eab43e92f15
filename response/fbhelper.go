package response

type ResData struct {
	IdType    int             `json:"id_type"`
	Summaries []*OrderSummary `json:"summaries"`
}

type OrderSummary struct {
	Id          interface{} `json:"id"`
	OrderNum    interface{} `json:"orderNum"`
	OrderAmount interface{} `json:"orderAmount"`
	CheckoutNum interface{} `json:"checkoutNum"`
}

type Res struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

type ResSummaryData struct {
	IdType    int           `json:"id_type"`
	Summaries []*ResSummary `json:"summaries"`
}

type ResSummary struct {
	Id    string `json:"id"`
	Count int64  `json:"count"`
}
