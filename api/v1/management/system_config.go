package management

import (
	"admin-backend/global"
	"admin-backend/model/common/response"
	"admin-backend/model/management"
	managementReq "admin-backend/model/management/request"
	"admin-backend/utils"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type SystemConfigApi struct{}

// CreateSystemConfig 创建系统配置表
// @Tags SystemConfig
// @Summary 创建系统配置表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body management.SystemConfig true "创建系统配置表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /systemConfig/createSystemConfig [post]
func (systemConfigApi *SystemConfigApi) CreateSystemConfig(c *gin.Context) {
	var systemConfig managementReq.SystemConfigRequest
	err := c.ShouldBindJSON(&systemConfig)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	var m management.SystemConfig
	m.Id = utils.GenerateUint64ID()
	m.ConfigKey = systemConfig.ConfigKey
	m.ConfigValue = systemConfig.ConfigValue
	m.Enabled = systemConfig.Enabled
	m.Website = systemConfig.Website
	err = systemConfigService.CreateSystemConfig(&m)
	if err != nil {
		global.Logger.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteSystemConfig 删除系统配置表
// @Tags SystemConfig
// @Summary 删除系统配置表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body management.SystemConfig true "删除系统配置表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /systemConfig/deleteSystemConfig [delete]
func (systemConfigApi *SystemConfigApi) DeleteSystemConfig(c *gin.Context) {
	id := c.Query("id")
	err := systemConfigService.DeleteSystemConfig(id)
	if err != nil {
		global.Logger.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteSystemConfigByIds 批量删除系统配置表
// @Tags SystemConfig
// @Summary 批量删除系统配置表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /systemConfig/deleteSystemConfigByIds [delete]
func (systemConfigApi *SystemConfigApi) DeleteSystemConfigByIds(c *gin.Context) {
	ids := c.QueryArray("ids[]")
	err := systemConfigService.DeleteSystemConfigByIds(ids)
	if err != nil {
		global.Logger.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateSystemConfig 更新系统配置表
// @Tags SystemConfig
// @Summary 更新系统配置表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body management.SystemConfig true "更新系统配置表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /systemConfig/updateSystemConfig [put]
func (systemConfigApi *SystemConfigApi) UpdateSystemConfig(c *gin.Context) {
	var systemConfig managementReq.SystemConfigRequest
	err := c.ShouldBindJSON(&systemConfig)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	id := *systemConfig.Id
	if id == "" {
		response.FailWithMessage("id不能为空", c)
		return
	}
	uid := utils.ParseUint64(id)

	var m management.SystemConfig
	m.Id = uid
	m.ConfigKey = systemConfig.ConfigKey
	m.ConfigValue = systemConfig.ConfigValue
	m.Enabled = systemConfig.Enabled
	m.Website = systemConfig.Website

	err = systemConfigService.UpdateSystemConfig(m)
	if err != nil {
		global.Logger.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindSystemConfig 用id查询系统配置表
// @Tags SystemConfig
// @Summary 用id查询系统配置表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询系统配置表"
// @Success 200 {object} response.Response{data=management.SystemConfig,msg=string} "查询成功"
// @Router /systemConfig/findSystemConfig [get]
func (systemConfigApi *SystemConfigApi) FindSystemConfig(c *gin.Context) {
	id := c.Query("id")
	resystemConfig, err := systemConfigService.GetSystemConfig(id)
	if err != nil {
		global.Logger.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(resystemConfig, c)
}

// GetSystemConfigList 分页获取系统配置表列表
// @Tags SystemConfig
// @Summary 分页获取系统配置表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query managementReq.SystemConfigSearch true "分页获取系统配置表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /systemConfig/getSystemConfigList [get]
func (systemConfigApi *SystemConfigApi) GetSystemConfigList(c *gin.Context) {
	var pageInfo managementReq.SystemConfigSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := systemConfigService.GetSystemConfigInfoList(pageInfo)
	if err != nil {
		global.Logger.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetSystemConfigPublic 不需要鉴权的系统配置表接口
// @Tags SystemConfig
// @Summary 不需要鉴权的系统配置表接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /systemConfig/getSystemConfigPublic [get]
func (systemConfigApi *SystemConfigApi) GetSystemConfigPublic(c *gin.Context) {
	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	systemConfigService.GetSystemConfigPublic()
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的系统配置表接口信息",
	}, "获取成功", c)
}
