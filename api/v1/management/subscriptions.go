package management

import (
	"admin-backend/global"
	"admin-backend/model/common/response"
	"admin-backend/model/management"
	managementReq "admin-backend/model/management/request"
	"admin-backend/utils"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type SubscriptionsApi struct{}

// CreateSubscriptions 创建subscriptions表
// @Tags Subscriptions
// @Summary 创建subscriptions表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body management.Subscriptions true "创建subscriptions表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /subscriptions/createSubscriptions [post]
func (subscriptionsApi *SubscriptionsApi) CreateSubscriptions(c *gin.Context) {
	var subscriptions managementReq.SubscriptionsRequest
	err := c.ShouldBindJSON(&subscriptions)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	var m management.Subscriptions
	m.Id = utils.GenerateUint64ID()
	m.Title = subscriptions.Title
	m.SubTitle = subscriptions.SubTitle
	m.Slogon = subscriptions.Slogon
	m.Desc = subscriptions.Desc
	m.Level = subscriptions.Level
	m.Days = subscriptions.Days
	m.Price = subscriptions.Price
	m.FirstPrice = subscriptions.FirstPrice
	m.Enabled = subscriptions.Enabled
	m.IsDefault = subscriptions.IsDefault
	m.UnlockDrama = subscriptions.UnlockDrama
	m.PlanType = subscriptions.PlanType
	m.UnlockDrama = subscriptions.UnlockDrama
	m.PlanType = subscriptions.PlanType

	err = subscriptionsService.CreateSubscriptions(&m)
	if err != nil {
		global.Logger.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteSubscriptions 删除subscriptions表
// @Tags Subscriptions
// @Summary 删除subscriptions表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body management.Subscriptions true "删除subscriptions表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /subscriptions/deleteSubscriptions [delete]
func (subscriptionsApi *SubscriptionsApi) DeleteSubscriptions(c *gin.Context) {
	id := c.Query("id")
	err := subscriptionsService.DeleteSubscriptions(id)
	if err != nil {
		global.Logger.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteSubscriptionsByIds 批量删除subscriptions表
// @Tags Subscriptions
// @Summary 批量删除subscriptions表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /subscriptions/deleteSubscriptionsByIds [delete]
func (subscriptionsApi *SubscriptionsApi) DeleteSubscriptionsByIds(c *gin.Context) {
	ids := c.QueryArray("ids[]")
	err := subscriptionsService.DeleteSubscriptionsByIds(ids)
	if err != nil {
		global.Logger.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateSubscriptions 更新subscriptions表
// @Tags Subscriptions
// @Summary 更新subscriptions表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body management.Subscriptions true "更新subscriptions表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /subscriptions/updateSubscriptions [put]
func (subscriptionsApi *SubscriptionsApi) UpdateSubscriptions(c *gin.Context) {
	var subscriptions managementReq.SubscriptionsRequest
	err := c.ShouldBindJSON(&subscriptions)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	uid := utils.ParseUint64(subscriptions.Id)
	if uid == 0 {
		response.FailWithMessage("id不能为空", c)
		return
	}

	var m management.Subscriptions
	m.Id = uid
	m.Title = subscriptions.Title
	m.SubTitle = subscriptions.SubTitle
	m.Slogon = subscriptions.Slogon
	m.Desc = subscriptions.Desc
	m.Level = subscriptions.Level
	m.Enabled = subscriptions.Enabled
	m.IsDefault = subscriptions.IsDefault

	err = subscriptionsService.UpdateSubscriptions(m)
	if err != nil {
		global.Logger.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindSubscriptions 用id查询subscriptions表
// @Tags Subscriptions
// @Summary 用id查询subscriptions表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询subscriptions表"
// @Success 200 {object} response.Response{data=management.Subscriptions,msg=string} "查询成功"
// @Router /subscriptions/findSubscriptions [get]
func (subscriptionsApi *SubscriptionsApi) FindSubscriptions(c *gin.Context) {
	id := c.Query("id")
	resubscriptions, err := subscriptionsService.GetSubscriptions(id)
	if err != nil {
		global.Logger.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(resubscriptions, c)
}

// GetSubscriptionsList 分页获取subscriptions表列表
// @Tags Subscriptions
// @Summary 分页获取subscriptions表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query managementReq.SubscriptionsSearch true "分页获取subscriptions表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /subscriptions/getSubscriptionsList [get]
func (subscriptionsApi *SubscriptionsApi) GetSubscriptionsList(c *gin.Context) {
	var pageInfo managementReq.SubscriptionsSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := subscriptionsService.GetSubscriptionsInfoList(pageInfo)
	if err != nil {
		global.Logger.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetSubscriptionsPublic 不需要鉴权的subscriptions表接口
// @Tags Subscriptions
// @Summary 不需要鉴权的subscriptions表接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /subscriptions/getSubscriptionsPublic [get]
func (subscriptionsApi *SubscriptionsApi) GetSubscriptionsPublic(c *gin.Context) {
	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	subscriptionsService.GetSubscriptionsPublic()
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的subscriptions表接口信息",
	}, "获取成功", c)
}
