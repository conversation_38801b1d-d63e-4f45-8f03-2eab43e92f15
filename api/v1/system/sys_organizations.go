package system

import (
	"admin-backend/global"
	"admin-backend/model/common/response"
	"admin-backend/model/management"
	"admin-backend/model/system"
	systemReq "admin-backend/model/system/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type OrganizationApi struct{}

// CreateOrganization 创建组织表
// @Tags Organization
// @Summary 创建组织表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body system.Organization true "创建组织表"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /organization/createOrganization [post]
func (organizationApi *OrganizationApi) CreateOrganization(c *gin.Context) {
	var organization system.Organization
	err := c.ShouldBindJSON(&organization)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	*organization.Enabled = true // 前端暂时没放开enabled, 后端默认为true
	err = organizationService.CreateOrganization(&organization)
	if err != nil {
		global.Logger.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteOrganization 删除组织表
// @Tags Organization
// @Summary 删除组织表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body system.Organization true "删除组织表"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /organization/deleteOrganization [delete]
func (organizationApi *OrganizationApi) DeleteOrganization(c *gin.Context) {
	orgId := c.Query("id")

	var adminIds []uint
	err := global.MainDB.Model(&system.SysUser{}).Where("organization_id = ?", orgId).Pluck("id", &adminIds).Error
	if err != nil {
		global.Logger.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	if len(adminIds) > 0 {
		response.FailWithMessage("该组织绑定了用户, 无法删除!", c)
		return
	}

	var websiteIds []uint64
	err = global.MustGetGlobalDBByDBName("businessdb").Model(&management.Websites{}).Where("organization_id = ?", orgId).
		Pluck("id", &websiteIds).Error
	if err != nil {
		global.Logger.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	if len(websiteIds) > 0 {
		response.FailWithMessage("该组织绑定了域名, 无法删除!", c)
		return
	}

	err = organizationService.DeleteOrganization(orgId)
	if err != nil {
		global.Logger.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteOrganizationByIds 批量删除组织表
// @Tags Organization
// @Summary 批量删除组织表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /organization/deleteOrganizationByIds [delete]
func (organizationApi *OrganizationApi) DeleteOrganizationByIds(c *gin.Context) {
	orgIds := c.QueryArray("ids[]")

	var adminIds []uint
	err := global.MainDB.Model(&system.SysUser{}).Where("organization_id IN (?)", orgIds).Select("id").Find(&adminIds).Error
	if err != nil {
		global.Logger.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	if len(adminIds) > 0 {
		response.FailWithMessage("有组织绑定了用户, 无法批量删除!", c)
		return
	}

	var websiteIds []uint64
	err = global.MustGetGlobalDBByDBName("businessdb").Model(&management.Websites{}).Where("organization_id IN (?)", orgIds).
		Pluck("id", &websiteIds).Error
	if err != nil {
		global.Logger.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	if len(websiteIds) > 0 {
		response.FailWithMessage("有组织绑定了域名, 无法批量删除!", c)
		return
	}

	err = organizationService.DeleteOrganizationByIds(orgIds)
	if err != nil {
		global.Logger.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateOrganization 更新组织表
// @Tags Organization
// @Summary 更新组织表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body system.Organization true "更新组织表"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /organization/updateOrganization [put]
func (organizationApi *OrganizationApi) UpdateOrganization(c *gin.Context) {
	var organization system.Organization
	err := c.ShouldBindJSON(&organization)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = organizationService.UpdateOrganization(organization)
	if err != nil {
		global.Logger.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindOrganization 用id查询组织表
// @Tags Organization
// @Summary 用id查询组织表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询组织表"
// @Success 200 {object} response.Response{data=system.Organization,msg=string} "查询成功"
// @Router /organization/findOrganization [get]
func (organizationApi *OrganizationApi) FindOrganization(c *gin.Context) {
	id := c.Query("id")
	reorganization, err := organizationService.GetOrganization(id)
	if err != nil {
		global.Logger.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(reorganization, c)
}

// GetOrganizationList 分页获取组织表列表
// @Tags Organization
// @Summary 分页获取组织表列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query systemReq.OrganizationSearch true "分页获取组织表列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /organization/getOrganizationList [get]
func (organizationApi *OrganizationApi) GetOrganizationList(c *gin.Context) {
	var pageInfo systemReq.OrganizationSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := organizationService.GetOrganizationInfoList(pageInfo)
	if err != nil {
		global.Logger.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetOrganizationPublic 不需要鉴权的组织表接口
// @Tags Organization
// @Summary 不需要鉴权的组织表接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /organization/getOrganizationPublic [get]
func (organizationApi *OrganizationApi) GetOrganizationPublic(c *gin.Context) {
	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	organizationService.GetOrganizationPublic()
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的组织表接口信息",
	}, "获取成功", c)
}
