package model

import (
	"fmt"
	"reflect"
	"runtime"
	"strings"
	"time"
	"x-short-server/config"
	"x-short-server/pkg/redis"
	"x-short-server/pkg/xerr"
	"x-short-server/util"

	"gorm.io/gorm"
)

type Video struct {
	Id               uint64    `gorm:"primary_key;autoIncrement:false;not null;default:0;comment:主键" json:"id"`
	VideoName        string    `gorm:"type:varchar(255);not null;default:'';comment:剧名" json:"video_name"`
	Cover            string    `gorm:"type:varchar(255);not null;default:'';comment:封面" json:"cover"`
	PlayCount        uint64    `gorm:"type:bigint;not null;default:0;comment:播放量" json:"play_count"`
	Favorite         uint64    `gorm:"type:bigint;not null;default:0;comment:收藏量" json:"favorite"`
	Share            uint64    `gorm:"type:bigint;not null;default:0;comment:分享量" json:"share"`
	DiscountEpisodes int       `gorm:"column:discount_episodes;type:int;not null;default:0;comment:折扣集数" json:"discount_episodes"`
	Description      string    `gorm:"type:text;not null;default:'';comment:描述" json:"description"`
	PublishedAt      time.Time `gorm:"type:timestamp;not null;index:idx_vdo_pbl;default:current_timestamp;comment:发布时间" json:"published_at"`
	CreatedAt        time.Time `gorm:"type:timestamp;not null;default:current_timestamp;comment:创建时间" json:"created_at"`
	UpdatedAt        time.Time `gorm:"type:timestamp;not null;default:current_timestamp on update current_timestamp;comment:更新时间" json:"updated_at"`
}

type (
	SearchOptions func(*gorm.DB)
)

func WithPublished() SearchOptions {
	return func(db *gorm.DB) {
		db.Where("published_at < ?", time.Now())
	}
}

func WithVideoName(videoName string, hidden bool) SearchOptions {
	return func(db *gorm.DB) {
		if hidden {
			db.Where(TableNameVideoHidden+".video_name like ?", "%"+videoName+"%")
		} else {
			db.Where("videos_normal.video_name like ?", "%"+videoName+"%")
		}
	}
}

func WithVideoListPagination(page, perPage int) SearchOptions {
	return func(db *gorm.DB) {
		if page > 30 {
			page = page % 30
		}
		if page == 0 {
			page = 1
		}
		if perPage == 0 || perPage > 20 {
			perPage = 10
		}
		db.Offset((page - 1) * perPage).Limit(perPage)
	}
}

func WithKeyword(keyword string, hidden bool) SearchOptions {
	return func(db *gorm.DB) {
		if hidden {
			db.Where(TableNameVideoHidden+".video_name like ?", "%"+keyword+"%")
		} else {
			db.Where("videos_normal.video_name like ?", "%"+keyword+"%")
		}
	}
}

func WithVideoTag(tagId uint64, hidden bool) SearchOptions {
	return func(db *gorm.DB) {
		if hidden {
			db.Joins("JOIN video_tag_link ON videos_hidden.id = video_tag_link.video_id").Where("video_tag_link.tag_id = ?", tagId)
		} else {
			db.Joins("JOIN video_tag_link ON videos_normal.id = video_tag_link.video_id").Where("video_tag_link.tag_id = ?", tagId)
		}
	}
}

func WithVideoCategory(categoryId uint64, hidden bool) SearchOptions {
	return func(db *gorm.DB) {
		if hidden {
			db.Joins("JOIN video_category_link ON videos_hidden.id = video_category_link.video_id").Where("video_category_link.category_id = ?", categoryId)
		} else {
			db.Joins("JOIN video_category_link ON videos_normal.id = video_category_link.video_id").Where("video_category_link.category_id = ?", categoryId)
		}
	}
}

func WithOrder(order string) SearchOptions {
	return func(db *gorm.DB) {
		db.Order(order)
	}
}

func WithUserFavorite(u *User) SearchOptions {
	return func(db *gorm.DB) {
		if !u.IsAdvanceUser() {
			db.Joins("JOIN user_favorite ON videos_normal.id = user_favorite.video_id").Where("user_favorite.user_id = ?", u.Id).Order("user_favorite.updated_at desc")
		} else {
			db.Joins("JOIN user_favorite ON videos_hidden.id = user_favorite.video_id").Where("user_favorite.user_id = ?", u.Id).Order("user_favorite.updated_at desc")
		}
	}
}

func WithUserPlayHistory(u *User) SearchOptions {
	return func(db *gorm.DB) {
		if !u.IsAdvanceUser() {
			db.Joins("JOIN user_play_history ON videos_normal.id = user_play_history.video_id").Where("user_play_history.user_id = ?", u.Id).Order("user_play_history.updated_at desc")
		} else {
			db.Joins("JOIN user_play_history ON videos_hidden.id = user_play_history.video_id").Where("user_play_history.user_id = ?", u.Id).Order("user_play_history.updated_at desc")
		}
	}
}

func (Video) TableName() string {
	return "videos_normal"
}

func GetVideoTableName(hidden bool) string {
	if hidden {
		return "videos_hidden"
	}
	return "videos_normal"
}

func NewVideoModel() *Video {
	return &Video{}
}

func (m *Video) GetById(id uint64) (*Video, error) {
	var video Video
	if err := DB().Model(m).Where("id = ?", id).First(&video).Error; err != nil {
		return nil, err
	}
	return &video, nil
}

func (m *Video) Count() (int64, error) {

	var (
		count    int64 = 0
		err      error
		cacheKey = redis.NewCacheKey("x-short-server", "modelvideo", "count", "normal")
	)

	if count = util.ParseInt64(cacheKey.Get()); count > 0 {
		return count, nil
	}

	if err = DB().Model(m).Count(&count).Error; err != nil {
		return 0, err
	}

	defer cacheKey.Set(fmt.Sprintf("%d", count), 30*time.Minute)

	return count, nil
}

func (m *Video) GetByIds(ids []uint64, hidden bool) ([]Video, error) {
	var videos []Video
	if hidden {
		if err := DB().Table(TableNameVideoHidden).Where("id IN (?)", ids).Where("published_at < ?", time.Now()).Find(&videos).Error; err != nil {
			if xerr.IsGormRecordNotFound(err) {
				return nil, nil
			}
			return nil, err
		}
	} else {
		if err := DB().Model(m).Where("id IN (?)", ids).Where("published_at < ?", time.Now()).Find(&videos).Error; err != nil {
			if xerr.IsGormRecordNotFound(err) {
				return nil, nil
			}
			return nil, err
		}
	}
	return videos, nil
}

func (m *Video) GetCover() string {
	if strings.HasPrefix(m.Cover, "http") {
		return m.Cover
	}
	if len(m.Cover) == 0 {
		return ""
	}
	return config.Cfg.DramaDomain + m.Cover
}

func (m *Video) Search(options ...SearchOptions) (int64, []Video, error) {
	var total int64
	db := DB().Model(&Video{})
	for _, option := range options {
		option(db)
	}
	var videos []Video
	if err := db.Find(&videos).Error; err != nil {
		return 0, nil, err
	}

	db = DB().Model(&Video{})
	for _, option := range options {
		funcName := runtime.FuncForPC(reflect.ValueOf(option).Pointer()).Name()
		if strings.Contains(funcName, "WithVideoListPagination") {
			continue
		}
		option(db)
	}
	if err := db.Count(&total).Error; err != nil {
		return 0, nil, err
	}

	return total, videos, nil
}

func (m *Video) Save() error {
	return DB().Create(m).Error
}

func (m *Video) IsFavorite(u *User) bool {
	var count int64
	if err := DB().Model(&UserFavoLink{}).Where("user_id = ? AND video_id = ?", u.Id, m.Id).Count(&count).Error; err != nil {
		return false
	}
	return count > 0
}

func (m *Video) GetPlayHisotry(u *User) (*UserHistLink, error) {
	var link UserHistLink
	if err := DB().Model(&UserHistLink{}).Where("user_id = ? AND video_id = ?", u.Id, m.Id).First(&link).Error; err != nil {
		return nil, err
	}
	return &link, nil
}
