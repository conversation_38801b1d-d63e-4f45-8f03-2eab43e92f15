package model

import (
	"time"
	"x-short-server/pkg/logger"
	"x-short-server/pkg/xerr"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type PaymentTransaction struct {
	Id            uint64    `gorm:"primary_key;autoIncrement:false;not null;default:0;comment:主键" json:"id"`
	UserId        uint64    `gorm:"column:user_id;type:bigint;not null;default:0;comment:用户ID" json:"user_id"`
	OrderId       uint64    `gorm:"column:order_id;type:bigint;index:idx_pt_ordid;not null;default:0;comment:订单ID" json:"order_id"`
	TransactionId string    `gorm:"column:transaction_id;index:idx_pt_taid;type:varchar(180);not null;default:'';comment:交易ID" json:"transaction_id"`
	Amount        uint64    `gorm:"column:amount;type:bigint;not null;default:0;comment:金额" json:"amount"`
	PaymentStatus string    `gorm:"column:payment_status;type:varchar(255);not null;default:'';comment:支付状态" json:"payment_status"`
	Attach        string    `gorm:"column:attach;type:text;comment:附加信息" json:"attach"`
	CreatedAt     time.Time `gorm:"type:timestamp;not null;default:current_timestamp;comment:创建时间" json:"created_at"`
	UpdatedAt     time.Time `gorm:"type:timestamp;not null;default:current_timestamp on update current_timestamp;comment:更新时间" json:"updated_at"`
}

func (PaymentTransaction) TableName() string {
	return "payment_transactions"
}

func NewPaymentTransaction() *PaymentTransaction {
	return &PaymentTransaction{}
}

func (m *PaymentTransaction) GetLatestByOrderId(orderId uint64) (*PaymentTransaction, error) {
	var data *PaymentTransaction = &PaymentTransaction{}
	err := DB().Where("order_id = ?", orderId).Order("created_at desc").First(data).Error
	if !xerr.IsGormRecordNotFound(err) {
		logger.Error("cannot_get_latest_payment_transaction_by_order_id", zap.Error(err), zap.Uint64("order_id", orderId))
	}
	return data, err
}

func (m *PaymentTransaction) Create(tx *gorm.DB) error {
	return tx.Create(m).Error
}

func (m *PaymentTransaction) GetByTransactionId(transactionId string) (*PaymentTransaction, error) {
	var data PaymentTransaction
	err := DB().Where("transaction_id = ?", transactionId).First(&data).Error
	if err != nil && !xerr.IsGormRecordNotFound(err) {
		logger.Error("cannot_get_payment_transaction_by_transaction_id", zap.Error(err), zap.String("transaction_id", transactionId))
	}
	return &data, err
}
