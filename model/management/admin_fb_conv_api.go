// 自动生成模板AdminFbConvApi
package management

// Facebook Pixel 转化 API 结构体  AdminFbConvApi
type AdminFbConvApi struct {
	Id          uint64 `gorm:"primary_key;autoIncrement:false;not null;default:0;comment:主键" json:"id"`
	AdminId     uint64 `gorm:"column:admin_id;type:bigint;not null;default:0;comment:管理员ID" json:"admin_id"`
	PixelId     string `gorm:"type:varchar(255);not null;default:'';comment:Pixel ID" json:"pixel_id"`
	AccessToken string `gorm:"type:varchar(255);not null;default:'';comment:Access Token" json:"access_token"`
	Platform    string `gorm:"column:platform;type:varchar(20);not null;default:'';comment:平台" json:"platform"`
	Website     string `gorm:"column:website;type:varchar(255);not null;default:'';comment:网站" json:"website"`
}

// TableName Facebook Pixel 转化 API AdminFbConvApi自定义表名 admin_fb_conv_api
func (AdminFbConvApi) TableName() string {
	return "admin_fb_conv_api"
}
