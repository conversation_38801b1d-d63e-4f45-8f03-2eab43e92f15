// 自动生成模板AdminRefererTokens
package management

import "time"

// 落地页配置 结构体  AdminRefererTokens
type AdminRefererTokens struct {
	Id           uint64    `json:"id" form:"id" gorm:"primarykey;column:id;comment:主键;size:20;"`                                 //主键
	RefererToken string    `json:"refererToken" form:"refererToken" gorm:"column:referer_token;comment:Referer Token;size:150;"` //Referer Token
	AdminId      uint64    `json:"adminId" form:"adminId" gorm:"column:admin_id;comment:管理员ID;size:20;"`                         //管理员ID
	Enabled      bool      `json:"enabled" form:"enabled" gorm:"column:enabled;comment:是否启用;"`                                   //是否启用
	CreatedAt    time.Time `json:"createdAt" form:"createdAt" gorm:"column:created_at;comment:创建时间;"`                            //创建时间
	UpdatedAt    time.Time `json:"updatedAt" form:"updatedAt" gorm:"column:updated_at;comment:更新时间;"`                            //更新时间
}

// TableName 落地页配置 AdminRefererTokens自定义表名 admin_referer_tokens
func (AdminRefererTokens) TableName() string {
	return "admin_referer_tokens"
}
