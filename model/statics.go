package model

import (
	"time"
)

type Statics struct {
	Id          uint64    `gorm:"primary_key;autoIncrement:false;not null;default:0;comment:主键" json:"id"`
	Action      string    `gorm:"type:varchar(100);index:idx_stat_act;not null;default:'';comment:动作" json:"action"`
	EventId     string    `gorm:"type:varchar(255);index:idx_stat_event_id;not null;default:'';comment:事件ID" json:"event_id"`
	PlayAt      time.Time `gorm:"type:timestamp;not null;index:idx_stat_time;default:current_timestamp;comment:发生时间" json:"play_at"`
	Uid         uint64    `gorm:"type:bigint;not null;default:0;comment:用户ID" json:"uid"`
	Raw         string    `gorm:"type:text;comment:原始信息" json:"raw"`
	UserAgent   string    `gorm:"type:varchar(255);not null;default:'';comment:User-Agent" json:"user_agent"`
	Ip          string    `gorm:"type:varchar(255);not null;default:'';comment:IP" json:"ip"`
	IsMobile    *bool     `gorm:"type:boolean;not null;default:false;comment:移动设备" json:"is_mobile"`
	UtmSource   string    `gorm:"type:varchar(255);not null;default:'';comment:UTM来源" json:"utm_source"`
	UtmMedium   string    `gorm:"type:varchar(255);not null;default:'';comment:UTM媒介" json:"utm_medium"`
	UtmCampaign string    `gorm:"type:varchar(255);not null;default:'';comment:UTM活动" json:"utm_campaign"`
	UtmTerm     string    `gorm:"type:varchar(255);not null;default:'';comment:UTM关键词" json:"utm_term"`
	UtmContent  string    `gorm:"type:varchar(255);not null;default:'';comment:UTM内容" json:"utm_content"`
	Fbclid      string    `gorm:"type:varchar(255);not null;default:'';comment:Facebook Click ID" json:"fbclid"`
	Gaclid      string    `gorm:"type:varchar(255);not null;default:'';comment:Google Click ID" json:"gaclid"`
	CreatedAt   time.Time `gorm:"type:timestamp;not null;default:current_timestamp;comment:创建时间" json:"created_at"`
	UpdatedAt   time.Time `gorm:"type:timestamp;not null;default:current_timestamp on update current_timestamp;comment:更新时间" json:"updated_at"`
}

func (Statics) TableName() string {
	return "statics"
}
