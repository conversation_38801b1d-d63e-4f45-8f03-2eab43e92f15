package model

import (
	"time"
	"x-short-server/util"
)

type PaymentOrderError struct {
	Id        uint64    `gorm:"primary_key;autoIncrement:false;not null;default:0;comment:主键" json:"id"`
	OrderId   uint64    `gorm:"type:bigint;index:idx_rh_order_id;not null;default:0;comment:订单ID" json:"order_id"`
	TransType string    `gorm:"type:varchar(50);not null;default:'';comment:交易类型" json:"trans_type"`
	TransId   string    `gorm:"type:varchar(50);not null;default:'';comment:交易ID" json:"trans_id"`
	ErrorCode string    `gorm:"type:varchar(50);not null;default:'';comment:错误码" json:"error_code"`
	ErrorMsg  string    `gorm:"type:text;not null;comment:错误信息" json:"error_msg"`
	CreatedAt time.Time `gorm:"type:timestamp;not null;default:current_timestamp;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time `gorm:"type:timestamp;not null;default:current_timestamp;comment:更新时间" json:"updated_at"`
}

func (PaymentOrderError) TableName() string {
	return "payment_order_error"
}

const (
	TransErrorTypeRenewal = "renewal"
	TransErrorTypeFirst   = "first"
)

const (
	ErrorCodeDuplicate      = "duplicate_fingerprint" // 重复指纹
	ErrorCodeNeedToken      = "need_token"            // 需要token
	ErrorCodeAuthFailed     = "auth_failed"           // 认证失败
	ErrorInvalidCard        = "invalid_card"          // 無效卡
	ErrorRequestFailed      = "request_failed"        // 请求失败
	ErrorComfirmFailed      = "comfirm_failed"        // 确认失败
	ErrorInstrumentDeclined = "INSTRUMENT_DECLINED"   // 被银行拒绝
	ErrorTransactionRefused = "TRANSACTION_REFUSED"   // 交易被拒绝
	ErrorPayerCannotPay     = "PAYER_CANNOT_PAY"      // 付款人不能付款
	ErrorPaymentDeclined    = "PAYMENT_DECLINED"      // 付款被拒绝
	ErrorInsufficientFunds  = "insufficient_funds"    // 资金不足
	ErrorCardDeclined       = "card_declined"         // 卡被拒绝
	ErrorDoNotHonour        = "do_not_honour"         // 不接受
	ErrorNeed3DS            = "need_3ds"              // 需要3DS验证
	ErrorUnknown            = "unknown"               // 未知错误
)

func SavePaymentOrderError(orderId uint64, transId string, transType string, errorCode string, errorMsg string) error {
	return DB().Create(&PaymentOrderError{
		Id:        util.GenerateUint64ID(),
		OrderId:   orderId,
		TransId:   transId,
		TransType: transType,
		ErrorCode: errorCode,
		ErrorMsg:  errorMsg,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}).Error
}
