package model

import (
	"encoding/json"
	"fmt"
	"time"
	"x-short-server/pkg/logger"
	"x-short-server/pkg/redis"
	"x-short-server/pkg/xerr"
	"x-short-server/util"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type UserUnlockedDrama struct {
	Id        uint64    `gorm:"primary_key;autoIncrement:false;not null;default:0;comment:主键" json:"id"`
	UserId    uint64    `gorm:"column:user_id;type:bigint;not null;index:idx_uud_uid;default:0;comment:用户ID" json:"user_id"`
	VideoId   uint64    `gorm:"column:video_id;type:bigint;not null;index:idx_uud_vid;default:0;comment:视频ID" json:"video_id"`
	ExpiredAt time.Time `gorm:"type:timestamp;comment:过期时间" json:"expired_at"`
	CreatedAt time.Time `gorm:"type:timestamp;not null;default:current_timestamp;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time `gorm:"type:timestamp;not null;default:current_timestamp on update current_timestamp;comment:更新时间" json:"updated_at"`
}

func (UserUnlockedDrama) TableName() string {
	return "users_unlocked_drama"
}

func NewUserUnlockedDrama() *UserUnlockedDrama {
	return &UserUnlockedDrama{}
}

func (m *UserUnlockedDrama) GetByUserIdAndVideoId(uid, vid uint64) (*UserUnlockedDrama, error) {
	err := DB().Where("user_id = ? AND video_id = ?", uid, vid).First(m).Error
	return m, err
}

func (m *UserUnlockedDrama) IsUnlocked() bool {
	return m.ExpiredAt.After(time.Now())
}

func (m *UserUnlockedDrama) InsertOrUpdate(db *gorm.DB, uid, vid uint64, exp time.Time) *UserUnlockedDrama {
	// check if exists
	d, err := m.GetByUserIdAndVideoId(uid, vid)
	if err != nil && !xerr.IsGormRecordNotFound(err) {
		return nil
	}
	if d.Id == 0 {
		m.Id = util.GenerateUint64ID()
		m.UserId = uid
		m.VideoId = vid
		m.ExpiredAt = exp
		db.Create(m)
		return m
	}
	// update
	d.ExpiredAt = exp
	db.Model(m).Where("id = ?", d.Id).Updates(map[string]interface{}{
		"expired_at": exp,
	})
	return d
}

func (m *UserUnlockedDrama) Delete(uid, vid uint64) error {
	return DB().Where("user_id = ? AND video_id = ?", uid, vid).Delete(m).Error
}

func UserUnlockedDramasToJson(d []UserUnlockedDrama) string {
	data, _ := json.Marshal(d)
	return string(data)
}

func UserUnlockedDramasFromJson(j string) []UserUnlockedDrama {
	var d []UserUnlockedDrama
	json.Unmarshal([]byte(j), &d)
	return d
}

func (m *UserUnlockedDrama) QueryAllByUserId(uid uint64) ([]UserUnlockedDrama, error) {

	cacheKey := redis.NewCacheKey("x-short-server", "user_unlocked_drama", "querybyuid", fmt.Sprintf("%d", uid))
	cache := cacheKey.Get()
	if len(cache) > 0 {
		var data []UserUnlockedDrama = UserUnlockedDramasFromJson(cache)
		return data, nil
	}

	var data []UserUnlockedDrama
	err := DB().Where("user_id = ?", uid).Find(&data).Error

	if err == nil {
		defer cacheKey.Set(UserUnlockedDramasToJson(data), 5*time.Minute)
	}

	return data, err
}

func (m *UserUnlockedDrama) DeleteCache(uid uint64) {
	cacheKey := redis.NewCacheKey("x-short-server", "user_unlocked_drama", "querybyuid", fmt.Sprintf("%d", m.UserId))
	err := cacheKey.Del()
	if err != nil {
		logger.Error("delete_user_unlocked_drama_cache", zap.Error(err), zap.Uint64("uid", m.UserId))
	}
}
