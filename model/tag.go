package model

import (
	"context"
	"encoding/json"
	"time"
	"x-short-server/pkg/logger"
	"x-short-server/pkg/redis"

	"go.uber.org/zap"
)

type Tag struct {
	Id        uint64    `gorm:"primary_key;autoIncrement:false;not null;default:0;comment:主键" json:"id"`
	TagName   string    `gorm:"type:varchar(255);not null;default:'';comment:分类名" json:"tag_name"`
	Sort      uint64    `gorm:"type:bigint;not null;default:0;comment:排序" json:"sort"`
	CreatedAt time.Time `gorm:"type:timestamp;not null;default:current_timestamp;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time `gorm:"type:timestamp;not null;default:current_timestamp on update current_timestamp;comment:更新时间" json:"updated_at"`
}

func (Tag) TableName() string {
	return "tags"
}

type TagWihtCount struct {
	Tag
	Count int `json:"count"`
}

func NewTagModel() *Tag {
	return &Tag{}
}

func (t *Tag) All() ([]Tag, error) {
	var tags []Tag
	if err := DB().Model(t).Order("sort desc").Find(&tags).Error; err != nil {
		return nil, err
	}
	return tags, nil
}

func (t *Tag) FilterEmpty(hidden bool) ([]TagWihtCount, error) {
	var (
		videoTable = GetVideoTableName(hidden)
		tags       []TagWihtCount
		err        error
		cacheKey   = redis.NewCacheKey("x-short-server", "model_tag", "fewithcnt", videoTable)
		bt         []byte
	)

	bt, err = Redis().Get(context.Background(), cacheKey.String()).Bytes()
	if err == nil && len(bt) > 0 {
		err = json.Unmarshal(bt, &tags)
		if err != nil {
			logger.Error("filter_empty_tags", zap.Error(err))
		} else {
			return tags, nil
		}
	}

	err = DB().Table("tags").Select("tags.id,tags.tag_name,count(1) as count").
		Joins("left join video_tag_link vtl on tags.id = vtl.tag_id").
		Joins("left join " + videoTable + " v on vtl.video_id = v.id").
		Group("tags.id,tags.tag_name").
		Order("count desc").Find(&tags).Error
	if err != nil {
		logger.Error("filter_empty_tags", zap.Error(err))
		return nil, err
	}

	bt, err = json.Marshal(tags)
	if err != nil {
		logger.Error("filter_empty_tags", zap.Error(err))
	} else {
		err = Redis().Set(context.Background(), cacheKey.String(), bt, time.Minute*30).Err()
		if err != nil {
			logger.Error("filter_empty_tags", zap.Error(err))
		}
	}

	return tags, nil
}

func (t *Tag) Save() error {
	return DB().Create(t).Error
}

func (t *Tag) Update(values interface{}) error {
	return DB().Model(t).Where("id = ?", t.Id).Updates(values).Error
}

func (t *Tag) Delete() error {
	logger.Info("delete_tags", zap.Any("tag", *t))
	return DB().Delete(t).Error
}

func (t *Tag) GetTagsByVideoID(vid uint64) ([]Tag, error) {
	var tags []Tag
	if err := DB().Model(&Tag{}).Joins("JOIN video_tag_link ON tags.id = video_tag_link.tag_id").Where("video_tag_link.video_id = ?", vid).Find(&tags).Error; err != nil {
		return nil, err
	}
	return tags, nil
}
