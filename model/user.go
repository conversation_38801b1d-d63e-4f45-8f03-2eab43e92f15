package model

import (
	"time"
	"x-short-server/pkg/logger"
	"x-short-server/pkg/xerr"

	"go.uber.org/zap"
)

type User struct {
	Id                   uint64     `gorm:"primary_key;autoIncrement:false;not null;default:0;comment:主键" json:"id"`
	Website              string     `gorm:"type:varchar(255);not null;default:'';comment:网站" json:"website"`
	Email                string     `gorm:"type:varchar(255);not null;default:'';comment:邮箱" json:"email"`
	Username             string     `gorm:"type:varchar(255);not null;default:'';comment:用户名" json:"username"`
	Avatar               string     `gorm:"type:varchar(255);not null;default:'';comment:头像" json:"avatar"`
	Sid                  string     `gorm:"type:varchar(255);not null;default:'';comment:三方登录 SID" json:"sid"`
	Password             string     `gorm:"type:varchar(255);not null;default:'';comment:密码" json:"password"`
	Referer              string     `gorm:"type:varchar(255);not null;default:'';comment:转化来源" json:"referer"`
	RefererRaw           string     `gorm:"type:varchar(255);not null;default:'';comment:详细来源" json:"referer_raw"`
	LastLoginIp          string     `gorm:"type:varchar(255);not null;default:'';comment:最后登录 IP" json:"last_login_ip"`
	LastLoginTime        *time.Time `gorm:"type:timestamp;not null;default:current_timestamp;comment:最后登录时间" json:"last_login_time"`
	Language             string     `gorm:"type:varchar(255);not null;default:'en-US';comment:偏好语言" json:"language"`
	IsAdvance            *bool      `gorm:"type:boolean;not null;default:false;comment:M站用户" json:"is_advance"`
	IsEmailVerified      *bool      `gorm:"type:boolean;not null;default:false;comment:E-mail验证标记" json:"is_email_verified"`
	IsAnonymous          *bool      `gorm:"type:boolean;not null;default:false;comment:匿名用户" json:"is_anonymous"`
	IsForbidPay          *bool      `gorm:"type:boolean;not null;default:false;comment:禁止支付" json:"is_forbid_pay"`
	SubscriptionExpireAt *time.Time `gorm:"type:timestamp;comment:订阅过期时间" json:"subscription_expire_at"`
	UnlockDrama          int64      `gorm:"column:unlock_drama;type:int;not null;default:0;comment:解锁的剧目数" json:"unlock_drama"` // 允许解锁的剧目数
	UserType             string     `gorm:"type:varchar(255);not null;default:'';comment:用户类型" json:"user_type"`                // 用户类型, monthly_limit, monthly_unlimit
	LockedAt             *time.Time `gorm:"type:timestamp;comment:封号" json:"locked_at"`
	Region               string     `gorm:"column:region;type:varchar(10);not null;default:'';comment:地区" json:"region"`
	CreatedAt            time.Time  `gorm:"type:timestamp;not null;default:current_timestamp;comment:创建时间" json:"created_at"`
	UpdatedAt            time.Time  `gorm:"type:timestamp;not null;default:current_timestamp on update current_timestamp;comment:更新时间" json:"updated_at"`
}

func (User) TableName() string {
	return "users"
}

func NewUserModel() *User {
	return &User{}
}

func (m *User) Create(u *User) error {
	return DB().Create(u).Error
}

func (m *User) IsAdvanceUser() bool {
	return m.IsAdvance != nil && *m.IsAdvance
}

func (m *User) IsAnonymousUser() bool {
	return m.IsAnonymous != nil && *m.IsAnonymous
}

func (m *User) IsForbidPayUser() bool {
	return m.IsForbidPay != nil && *m.IsForbidPay
}

func (m *User) FindByUserId(id uint64) (bool, *User, error) {
	var user User
	err := DB().Where("id = ?", id).First(&user).Error
	if err != nil {
		return false, nil, err
	}
	if user.Id == 0 {
		return false, nil, nil
	}
	return true, &user, nil
}

func (m *User) IsSubscribe() bool {
	return m.SubscriptionExpireAt != nil && m.SubscriptionExpireAt.After(time.Now())
}

func (m *User) IsBlocked() bool {
	return m.LockedAt != nil && m.LockedAt.After(time.Now())
}

func (m *User) FindByFacebookID(id, website string) (bool, *User, error) {
	var user User
	err := DB().Where("sid = ? and website = ?", id, website).First(&user).Error
	if err != nil && xerr.IsGormRecordNotFound(err) {
		return false, nil, nil
	} else if err != nil {
		logger.Error("find_user_by_facebook_id_failed", zap.Error(err), zap.String("facebook_id", id))
		return false, nil, err
	}
	if user.Id == 0 {
		return false, nil, nil
	}
	return true, &user, nil
}

func (m *User) FindByEmail(email string) (bool, *User, error) {
	var user User
	err := DB().Where("email = ?", email).First(&user).Error
	if err != nil && xerr.IsGormRecordNotFound(err) {
		return false, nil, nil
	} else if err != nil {
		logger.Error("find_user_by_email_failed", zap.Error(err), zap.String("email", email))
		return false, nil, err
	}
	if user.Id == 0 {
		return false, nil, nil
	}
	return true, &user, nil
}

func (s *User) GetUserByEmailAndHost(email, host string) (bool, *User, error) {
	var u *User = &User{}
	err := DB().Model(u).Where("email = ? AND website = ?", email, host).First(u).Error
	if xerr.IsGormRecordNotFound(err) {
		return false, nil, nil
	} else if err != nil {
		return false, nil, err
	}
	return true, u, nil
}

func (m *User) Updates(data map[string]interface{}) error {
	return DB().Model(m).Where("id = ?", m.Id).Updates(data).Error
}

func (m *User) GetLatestPaymentOrder() (*PaymentOrder, error) {
	var order PaymentOrder
	err := DB().Where("user_id = ? and order_status in ?", m.Id, []string{PaymentOrderStatusPaid, PaymentOrderStatusCancel, PaymentOrderStatusSubed}).Order("id DESC").First(&order).Error
	if err != nil && xerr.IsGormRecordNotFound(err) {
		return nil, nil
	} else if err != nil {
		logger.Error("get_latest_payment_order_failed", zap.Error(err), zap.Uint64("user_id", m.Id))
		return nil, err
	}
	return &order, nil
}

// CouldUpgradeSubscription 检查用户是否可以升级订阅
// 1. 如果是限制用户，且已经订阅，则可以升级
// 2. 如果是普通用户，且有一次性订阅，则可以升级
func (m *User) CouldUpgradeSubscription() bool {
	if m.UserType == SubscriptionTypeLimit && m.IsSubscribe() {
		return true
	}
	if m.UserType == SubscriptionTypeOnetime {
		return true
	}
	if m.UserType == "" {
		order, err := m.GetLatestPaymentOrder()
		if err != nil {
			logger.Error("get_latest_payment_order_failed", zap.Error(err), zap.Uint64("user_id", m.Id))
		}
		if order != nil && order.PlanType == SubscriptionTypeOnetime {
			return true
		}
	}
	return false
}
