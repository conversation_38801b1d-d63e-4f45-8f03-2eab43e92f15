package model

import (
	"encoding/json"
	"time"
	"x-short-server/pkg/redis"
)

const (
	PlatformFacebook = "facebook"
	PlatformTiktok   = "tiktok"
	PlatformBigo     = "bigo"
)

type AdminConversion struct {
	Id          uint64    `gorm:"primary_key;autoIncrement:false;not null;default:0;comment:主键" json:"id"`
	AdminId     uint64    `gorm:"column:admin_id;type:bigint;not null;default:0;comment:管理员ID" json:"admin_id"`
	PixelId     string    `gorm:"type:varchar(255);not null;default:'';comment:Pixel ID" json:"pixel_id"`
	AccessToken string    `gorm:"type:varchar(255);not null;default:'';comment:Access Token" json:"access_token"`
	Enabled     *bool     `gorm:"type:boolean;not null;default:false;comment:是否启用" json:"enabled"`
	Website     string    `json:"website" gorm:"column:website;type:varchar(150);not null;default:'';comment:网站"`
	CreatedAt   time.Time `gorm:"type:timestamp;not null;default:current_timestamp;comment:创建时间" json:"created_at"`
	UpdatedAt   time.Time `gorm:"type:timestamp;not null;default:current_timestamp on update current_timestamp;comment:更新时间" json:"updated_at"`
	Platform    string    `gorm:"column:platform;type:varchar(20);not null;default:'';comment:平台" json:"platform"`
}

func (AdminConversion) TableName() string {
	return "admin_fb_conv_api"
}

func NewAdminConversion() *AdminConversion {
	return &AdminConversion{}
}

func (m *AdminConversion) List(domain string) []AdminConversion {

	var (
		fbConv             []AdminConversion
		conversionCacheKey = redis.NewCacheKey("x-short-server", "model", "list-conversion", domain)
	)
	str := conversionCacheKey.Get()
	if len(str) > 0 {
		_ = json.Unmarshal([]byte(str), &fbConv)
		return fbConv
	}

	if domain == "" {
		DB().Find(&fbConv, "enabled = ?", true)
	} else {
		DB().Find(&fbConv, "enabled = ? and (website = ? or website = '*')", true, domain)
	}

	// 去重
	uniqueList := make(map[string]struct{})
	var retList []AdminConversion
	for _, v := range fbConv {
		if v.PixelId == "" {
			continue
		}
		if _, ok := uniqueList[v.PixelId]; ok {
			continue
		}
		uniqueList[v.PixelId] = struct{}{}
		retList = append(retList, v)
	}

	_ = conversionCacheKey.SetObject(retList, 5*time.Minute)

	return retList
}

func (m *AdminConversion) Get(id string) (AdminConversion, error) {
	var fbConv AdminConversion
	err := DB().First(&fbConv, "id = ?", id).Error
	return fbConv, err
}

func (m *AdminConversion) Delete(id string) error {

	var conv AdminConversion
	DB().Model(&AdminConversion{}).Where("id = ?", id).First(&conv)
	conversionCacheKey := redis.NewCacheKey("x-short-server", "model", "list-conversion", conv.Website)
	conversionCacheKey.Del()

	return DB().Model(m).Where("id = ?", id).Delete(m).Error
}
