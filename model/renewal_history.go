package model

import "time"

const (
	RenewalStatusSucceess = "success"
	RenewalStatusFailed   = "failed"
)

type RenewalHistory struct {
	Id            uint64    `gorm:"primary_key;autoIncrement:false;not null;default:0;comment:主键" json:"id"`
	OrderId       uint64    `gorm:"type:bigint;index:idx_rh_order_id;not null;default:0;comment:订单ID" json:"order_id"`
	RenewalTimes  uint64    `gorm:"type:bigint;not null;default:0;comment:续费次数" json:"renewal_times"`
	RenewalStatus string    `gorm:"type:varchar(50);not null;default:'';comment:续费状态" json:"renewal_status"`
	CreatedAt     time.Time `gorm:"type:timestamp;not null;default:current_timestamp;comment:创建时间" json:"created_at"`
	UpdatedAt     time.Time `gorm:"type:timestamp;not null;default:current_timestamp;comment:更新时间" json:"updated_at"`
}

func (m *RenewalHistory) TableName() string {
	return "renewal_history"
}

func (m *RenewalHistory) GetByOrderIdAndTimes(orderId uint64, times uint64) (*RenewalHistory, error) {
	var rh RenewalHistory
	if err := DB().Where("order_id = ? AND renewal_times = ?", orderId, times).First(&rh).Error; err != nil {
		return nil, err
	}
	return &rh, nil
}

func (m *RenewalHistory) UpdateStatus(status string) error {
	return DB().Model(m).Where("id = ?", m.Id).Update("renewal_status", status).Error
}

func (m *RenewalHistory) Save() error {
	return DB().Create(m).Error
}
