// Code generated by ent, DO NOT EDIT.

package davaccount

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/cloudreve/Cloudreve/v4/ent/predicate"
	"github.com/cloudreve/Cloudreve/v4/pkg/boolset"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldEQ(FieldUpdatedAt, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldEQ(FieldDeletedAt, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldEQ(FieldName, v))
}

// URI applies equality check predicate on the "uri" field. It's identical to URIEQ.
func URI(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldEQ(FieldURI, v))
}

// Password applies equality check predicate on the "password" field. It's identical to PasswordEQ.
func Password(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldEQ(FieldPassword, v))
}

// Options applies equality check predicate on the "options" field. It's identical to OptionsEQ.
func Options(v *boolset.BooleanSet) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldEQ(FieldOptions, v))
}

// OwnerID applies equality check predicate on the "owner_id" field. It's identical to OwnerIDEQ.
func OwnerID(v int) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldEQ(FieldOwnerID, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldLTE(FieldUpdatedAt, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.DavAccount {
	return predicate.DavAccount(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.DavAccount {
	return predicate.DavAccount(sql.FieldNotNull(FieldDeletedAt))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldContainsFold(FieldName, v))
}

// URIEQ applies the EQ predicate on the "uri" field.
func URIEQ(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldEQ(FieldURI, v))
}

// URINEQ applies the NEQ predicate on the "uri" field.
func URINEQ(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldNEQ(FieldURI, v))
}

// URIIn applies the In predicate on the "uri" field.
func URIIn(vs ...string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldIn(FieldURI, vs...))
}

// URINotIn applies the NotIn predicate on the "uri" field.
func URINotIn(vs ...string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldNotIn(FieldURI, vs...))
}

// URIGT applies the GT predicate on the "uri" field.
func URIGT(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldGT(FieldURI, v))
}

// URIGTE applies the GTE predicate on the "uri" field.
func URIGTE(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldGTE(FieldURI, v))
}

// URILT applies the LT predicate on the "uri" field.
func URILT(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldLT(FieldURI, v))
}

// URILTE applies the LTE predicate on the "uri" field.
func URILTE(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldLTE(FieldURI, v))
}

// URIContains applies the Contains predicate on the "uri" field.
func URIContains(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldContains(FieldURI, v))
}

// URIHasPrefix applies the HasPrefix predicate on the "uri" field.
func URIHasPrefix(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldHasPrefix(FieldURI, v))
}

// URIHasSuffix applies the HasSuffix predicate on the "uri" field.
func URIHasSuffix(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldHasSuffix(FieldURI, v))
}

// URIEqualFold applies the EqualFold predicate on the "uri" field.
func URIEqualFold(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldEqualFold(FieldURI, v))
}

// URIContainsFold applies the ContainsFold predicate on the "uri" field.
func URIContainsFold(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldContainsFold(FieldURI, v))
}

// PasswordEQ applies the EQ predicate on the "password" field.
func PasswordEQ(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldEQ(FieldPassword, v))
}

// PasswordNEQ applies the NEQ predicate on the "password" field.
func PasswordNEQ(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldNEQ(FieldPassword, v))
}

// PasswordIn applies the In predicate on the "password" field.
func PasswordIn(vs ...string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldIn(FieldPassword, vs...))
}

// PasswordNotIn applies the NotIn predicate on the "password" field.
func PasswordNotIn(vs ...string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldNotIn(FieldPassword, vs...))
}

// PasswordGT applies the GT predicate on the "password" field.
func PasswordGT(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldGT(FieldPassword, v))
}

// PasswordGTE applies the GTE predicate on the "password" field.
func PasswordGTE(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldGTE(FieldPassword, v))
}

// PasswordLT applies the LT predicate on the "password" field.
func PasswordLT(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldLT(FieldPassword, v))
}

// PasswordLTE applies the LTE predicate on the "password" field.
func PasswordLTE(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldLTE(FieldPassword, v))
}

// PasswordContains applies the Contains predicate on the "password" field.
func PasswordContains(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldContains(FieldPassword, v))
}

// PasswordHasPrefix applies the HasPrefix predicate on the "password" field.
func PasswordHasPrefix(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldHasPrefix(FieldPassword, v))
}

// PasswordHasSuffix applies the HasSuffix predicate on the "password" field.
func PasswordHasSuffix(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldHasSuffix(FieldPassword, v))
}

// PasswordEqualFold applies the EqualFold predicate on the "password" field.
func PasswordEqualFold(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldEqualFold(FieldPassword, v))
}

// PasswordContainsFold applies the ContainsFold predicate on the "password" field.
func PasswordContainsFold(v string) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldContainsFold(FieldPassword, v))
}

// OptionsEQ applies the EQ predicate on the "options" field.
func OptionsEQ(v *boolset.BooleanSet) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldEQ(FieldOptions, v))
}

// OptionsNEQ applies the NEQ predicate on the "options" field.
func OptionsNEQ(v *boolset.BooleanSet) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldNEQ(FieldOptions, v))
}

// OptionsIn applies the In predicate on the "options" field.
func OptionsIn(vs ...*boolset.BooleanSet) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldIn(FieldOptions, vs...))
}

// OptionsNotIn applies the NotIn predicate on the "options" field.
func OptionsNotIn(vs ...*boolset.BooleanSet) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldNotIn(FieldOptions, vs...))
}

// OptionsGT applies the GT predicate on the "options" field.
func OptionsGT(v *boolset.BooleanSet) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldGT(FieldOptions, v))
}

// OptionsGTE applies the GTE predicate on the "options" field.
func OptionsGTE(v *boolset.BooleanSet) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldGTE(FieldOptions, v))
}

// OptionsLT applies the LT predicate on the "options" field.
func OptionsLT(v *boolset.BooleanSet) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldLT(FieldOptions, v))
}

// OptionsLTE applies the LTE predicate on the "options" field.
func OptionsLTE(v *boolset.BooleanSet) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldLTE(FieldOptions, v))
}

// PropsIsNil applies the IsNil predicate on the "props" field.
func PropsIsNil() predicate.DavAccount {
	return predicate.DavAccount(sql.FieldIsNull(FieldProps))
}

// PropsNotNil applies the NotNil predicate on the "props" field.
func PropsNotNil() predicate.DavAccount {
	return predicate.DavAccount(sql.FieldNotNull(FieldProps))
}

// OwnerIDEQ applies the EQ predicate on the "owner_id" field.
func OwnerIDEQ(v int) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldEQ(FieldOwnerID, v))
}

// OwnerIDNEQ applies the NEQ predicate on the "owner_id" field.
func OwnerIDNEQ(v int) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldNEQ(FieldOwnerID, v))
}

// OwnerIDIn applies the In predicate on the "owner_id" field.
func OwnerIDIn(vs ...int) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldIn(FieldOwnerID, vs...))
}

// OwnerIDNotIn applies the NotIn predicate on the "owner_id" field.
func OwnerIDNotIn(vs ...int) predicate.DavAccount {
	return predicate.DavAccount(sql.FieldNotIn(FieldOwnerID, vs...))
}

// HasOwner applies the HasEdge predicate on the "owner" edge.
func HasOwner() predicate.DavAccount {
	return predicate.DavAccount(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, OwnerTable, OwnerColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasOwnerWith applies the HasEdge predicate on the "owner" edge with a given conditions (other predicates).
func HasOwnerWith(preds ...predicate.User) predicate.DavAccount {
	return predicate.DavAccount(func(s *sql.Selector) {
		step := newOwnerStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.DavAccount) predicate.DavAccount {
	return predicate.DavAccount(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.DavAccount) predicate.DavAccount {
	return predicate.DavAccount(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.DavAccount) predicate.DavAccount {
	return predicate.DavAccount(sql.NotPredicates(p))
}
