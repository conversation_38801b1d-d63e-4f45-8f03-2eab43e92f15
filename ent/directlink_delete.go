// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/cloudreve/Cloudreve/v4/ent/directlink"
	"github.com/cloudreve/Cloudreve/v4/ent/predicate"
)

// DirectLinkDelete is the builder for deleting a DirectLink entity.
type DirectLinkDelete struct {
	config
	hooks    []Hook
	mutation *DirectLinkMutation
}

// Where appends a list predicates to the DirectLinkDelete builder.
func (dld *DirectLinkDelete) Where(ps ...predicate.DirectLink) *DirectLinkDelete {
	dld.mutation.Where(ps...)
	return dld
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (dld *DirectLinkDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, dld.sqlExec, dld.mutation, dld.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (dld *DirectLinkDelete) ExecX(ctx context.Context) int {
	n, err := dld.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (dld *DirectLinkDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(directlink.Table, sqlgraph.NewFieldSpec(directlink.FieldID, field.TypeInt))
	if ps := dld.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, dld.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	dld.mutation.done = true
	return affected, err
}

// DirectLinkDeleteOne is the builder for deleting a single DirectLink entity.
type DirectLinkDeleteOne struct {
	dld *DirectLinkDelete
}

// Where appends a list predicates to the DirectLinkDelete builder.
func (dldo *DirectLinkDeleteOne) Where(ps ...predicate.DirectLink) *DirectLinkDeleteOne {
	dldo.dld.mutation.Where(ps...)
	return dldo
}

// Exec executes the deletion query.
func (dldo *DirectLinkDeleteOne) Exec(ctx context.Context) error {
	n, err := dldo.dld.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{directlink.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (dldo *DirectLinkDeleteOne) ExecX(ctx context.Context) {
	if err := dldo.Exec(ctx); err != nil {
		panic(err)
	}
}
