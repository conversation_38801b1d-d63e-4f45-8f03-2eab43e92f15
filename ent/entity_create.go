// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/cloudreve/Cloudreve/v4/ent/entity"
	"github.com/cloudreve/Cloudreve/v4/ent/file"
	"github.com/cloudreve/Cloudreve/v4/ent/storagepolicy"
	"github.com/cloudreve/Cloudreve/v4/ent/user"
	"github.com/cloudreve/Cloudreve/v4/inventory/types"
	"github.com/gofrs/uuid"
)

// EntityCreate is the builder for creating a Entity entity.
type EntityCreate struct {
	config
	mutation *EntityMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedAt sets the "created_at" field.
func (ec *EntityCreate) SetCreatedAt(t time.Time) *EntityCreate {
	ec.mutation.SetCreatedAt(t)
	return ec
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (ec *EntityCreate) SetNillableCreatedAt(t *time.Time) *EntityCreate {
	if t != nil {
		ec.SetCreatedAt(*t)
	}
	return ec
}

// SetUpdatedAt sets the "updated_at" field.
func (ec *EntityCreate) SetUpdatedAt(t time.Time) *EntityCreate {
	ec.mutation.SetUpdatedAt(t)
	return ec
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (ec *EntityCreate) SetNillableUpdatedAt(t *time.Time) *EntityCreate {
	if t != nil {
		ec.SetUpdatedAt(*t)
	}
	return ec
}

// SetDeletedAt sets the "deleted_at" field.
func (ec *EntityCreate) SetDeletedAt(t time.Time) *EntityCreate {
	ec.mutation.SetDeletedAt(t)
	return ec
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (ec *EntityCreate) SetNillableDeletedAt(t *time.Time) *EntityCreate {
	if t != nil {
		ec.SetDeletedAt(*t)
	}
	return ec
}

// SetType sets the "type" field.
func (ec *EntityCreate) SetType(i int) *EntityCreate {
	ec.mutation.SetType(i)
	return ec
}

// SetSource sets the "source" field.
func (ec *EntityCreate) SetSource(s string) *EntityCreate {
	ec.mutation.SetSource(s)
	return ec
}

// SetSize sets the "size" field.
func (ec *EntityCreate) SetSize(i int64) *EntityCreate {
	ec.mutation.SetSize(i)
	return ec
}

// SetReferenceCount sets the "reference_count" field.
func (ec *EntityCreate) SetReferenceCount(i int) *EntityCreate {
	ec.mutation.SetReferenceCount(i)
	return ec
}

// SetNillableReferenceCount sets the "reference_count" field if the given value is not nil.
func (ec *EntityCreate) SetNillableReferenceCount(i *int) *EntityCreate {
	if i != nil {
		ec.SetReferenceCount(*i)
	}
	return ec
}

// SetStoragePolicyEntities sets the "storage_policy_entities" field.
func (ec *EntityCreate) SetStoragePolicyEntities(i int) *EntityCreate {
	ec.mutation.SetStoragePolicyEntities(i)
	return ec
}

// SetCreatedBy sets the "created_by" field.
func (ec *EntityCreate) SetCreatedBy(i int) *EntityCreate {
	ec.mutation.SetCreatedBy(i)
	return ec
}

// SetNillableCreatedBy sets the "created_by" field if the given value is not nil.
func (ec *EntityCreate) SetNillableCreatedBy(i *int) *EntityCreate {
	if i != nil {
		ec.SetCreatedBy(*i)
	}
	return ec
}

// SetUploadSessionID sets the "upload_session_id" field.
func (ec *EntityCreate) SetUploadSessionID(u uuid.UUID) *EntityCreate {
	ec.mutation.SetUploadSessionID(u)
	return ec
}

// SetNillableUploadSessionID sets the "upload_session_id" field if the given value is not nil.
func (ec *EntityCreate) SetNillableUploadSessionID(u *uuid.UUID) *EntityCreate {
	if u != nil {
		ec.SetUploadSessionID(*u)
	}
	return ec
}

// SetRecycleOptions sets the "recycle_options" field.
func (ec *EntityCreate) SetRecycleOptions(tro *types.EntityRecycleOption) *EntityCreate {
	ec.mutation.SetRecycleOptions(tro)
	return ec
}

// AddFileIDs adds the "file" edge to the File entity by IDs.
func (ec *EntityCreate) AddFileIDs(ids ...int) *EntityCreate {
	ec.mutation.AddFileIDs(ids...)
	return ec
}

// AddFile adds the "file" edges to the File entity.
func (ec *EntityCreate) AddFile(f ...*File) *EntityCreate {
	ids := make([]int, len(f))
	for i := range f {
		ids[i] = f[i].ID
	}
	return ec.AddFileIDs(ids...)
}

// SetUserID sets the "user" edge to the User entity by ID.
func (ec *EntityCreate) SetUserID(id int) *EntityCreate {
	ec.mutation.SetUserID(id)
	return ec
}

// SetNillableUserID sets the "user" edge to the User entity by ID if the given value is not nil.
func (ec *EntityCreate) SetNillableUserID(id *int) *EntityCreate {
	if id != nil {
		ec = ec.SetUserID(*id)
	}
	return ec
}

// SetUser sets the "user" edge to the User entity.
func (ec *EntityCreate) SetUser(u *User) *EntityCreate {
	return ec.SetUserID(u.ID)
}

// SetStoragePolicyID sets the "storage_policy" edge to the StoragePolicy entity by ID.
func (ec *EntityCreate) SetStoragePolicyID(id int) *EntityCreate {
	ec.mutation.SetStoragePolicyID(id)
	return ec
}

// SetStoragePolicy sets the "storage_policy" edge to the StoragePolicy entity.
func (ec *EntityCreate) SetStoragePolicy(s *StoragePolicy) *EntityCreate {
	return ec.SetStoragePolicyID(s.ID)
}

// Mutation returns the EntityMutation object of the builder.
func (ec *EntityCreate) Mutation() *EntityMutation {
	return ec.mutation
}

// Save creates the Entity in the database.
func (ec *EntityCreate) Save(ctx context.Context) (*Entity, error) {
	if err := ec.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, ec.sqlSave, ec.mutation, ec.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (ec *EntityCreate) SaveX(ctx context.Context) *Entity {
	v, err := ec.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ec *EntityCreate) Exec(ctx context.Context) error {
	_, err := ec.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ec *EntityCreate) ExecX(ctx context.Context) {
	if err := ec.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ec *EntityCreate) defaults() error {
	if _, ok := ec.mutation.CreatedAt(); !ok {
		if entity.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized entity.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := entity.DefaultCreatedAt()
		ec.mutation.SetCreatedAt(v)
	}
	if _, ok := ec.mutation.UpdatedAt(); !ok {
		if entity.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized entity.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := entity.DefaultUpdatedAt()
		ec.mutation.SetUpdatedAt(v)
	}
	if _, ok := ec.mutation.ReferenceCount(); !ok {
		v := entity.DefaultReferenceCount
		ec.mutation.SetReferenceCount(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (ec *EntityCreate) check() error {
	if _, ok := ec.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Entity.created_at"`)}
	}
	if _, ok := ec.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Entity.updated_at"`)}
	}
	if _, ok := ec.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "Entity.type"`)}
	}
	if _, ok := ec.mutation.Source(); !ok {
		return &ValidationError{Name: "source", err: errors.New(`ent: missing required field "Entity.source"`)}
	}
	if _, ok := ec.mutation.Size(); !ok {
		return &ValidationError{Name: "size", err: errors.New(`ent: missing required field "Entity.size"`)}
	}
	if _, ok := ec.mutation.ReferenceCount(); !ok {
		return &ValidationError{Name: "reference_count", err: errors.New(`ent: missing required field "Entity.reference_count"`)}
	}
	if _, ok := ec.mutation.StoragePolicyEntities(); !ok {
		return &ValidationError{Name: "storage_policy_entities", err: errors.New(`ent: missing required field "Entity.storage_policy_entities"`)}
	}
	if _, ok := ec.mutation.StoragePolicyID(); !ok {
		return &ValidationError{Name: "storage_policy", err: errors.New(`ent: missing required edge "Entity.storage_policy"`)}
	}
	return nil
}

func (ec *EntityCreate) sqlSave(ctx context.Context) (*Entity, error) {
	if err := ec.check(); err != nil {
		return nil, err
	}
	_node, _spec := ec.createSpec()
	if err := sqlgraph.CreateNode(ctx, ec.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	ec.mutation.id = &_node.ID
	ec.mutation.done = true
	return _node, nil
}

func (ec *EntityCreate) createSpec() (*Entity, *sqlgraph.CreateSpec) {
	var (
		_node = &Entity{config: ec.config}
		_spec = sqlgraph.NewCreateSpec(entity.Table, sqlgraph.NewFieldSpec(entity.FieldID, field.TypeInt))
	)

	if id, ok := ec.mutation.ID(); ok {
		_node.ID = id
		id64 := int64(id)
		_spec.ID.Value = id64
	}

	_spec.OnConflict = ec.conflict
	if value, ok := ec.mutation.CreatedAt(); ok {
		_spec.SetField(entity.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := ec.mutation.UpdatedAt(); ok {
		_spec.SetField(entity.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := ec.mutation.DeletedAt(); ok {
		_spec.SetField(entity.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = &value
	}
	if value, ok := ec.mutation.GetType(); ok {
		_spec.SetField(entity.FieldType, field.TypeInt, value)
		_node.Type = value
	}
	if value, ok := ec.mutation.Source(); ok {
		_spec.SetField(entity.FieldSource, field.TypeString, value)
		_node.Source = value
	}
	if value, ok := ec.mutation.Size(); ok {
		_spec.SetField(entity.FieldSize, field.TypeInt64, value)
		_node.Size = value
	}
	if value, ok := ec.mutation.ReferenceCount(); ok {
		_spec.SetField(entity.FieldReferenceCount, field.TypeInt, value)
		_node.ReferenceCount = value
	}
	if value, ok := ec.mutation.UploadSessionID(); ok {
		_spec.SetField(entity.FieldUploadSessionID, field.TypeUUID, value)
		_node.UploadSessionID = &value
	}
	if value, ok := ec.mutation.RecycleOptions(); ok {
		_spec.SetField(entity.FieldRecycleOptions, field.TypeJSON, value)
		_node.RecycleOptions = value
	}
	if nodes := ec.mutation.FileIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   entity.FileTable,
			Columns: entity.FilePrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(file.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := ec.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   entity.UserTable,
			Columns: []string{entity.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.CreatedBy = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := ec.mutation.StoragePolicyIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   entity.StoragePolicyTable,
			Columns: []string{entity.StoragePolicyColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(storagepolicy.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.StoragePolicyEntities = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Entity.Create().
//		SetCreatedAt(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.EntityUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (ec *EntityCreate) OnConflict(opts ...sql.ConflictOption) *EntityUpsertOne {
	ec.conflict = opts
	return &EntityUpsertOne{
		create: ec,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Entity.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (ec *EntityCreate) OnConflictColumns(columns ...string) *EntityUpsertOne {
	ec.conflict = append(ec.conflict, sql.ConflictColumns(columns...))
	return &EntityUpsertOne{
		create: ec,
	}
}

type (
	// EntityUpsertOne is the builder for "upsert"-ing
	//  one Entity node.
	EntityUpsertOne struct {
		create *EntityCreate
	}

	// EntityUpsert is the "OnConflict" setter.
	EntityUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedAt sets the "updated_at" field.
func (u *EntityUpsert) SetUpdatedAt(v time.Time) *EntityUpsert {
	u.Set(entity.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *EntityUpsert) UpdateUpdatedAt() *EntityUpsert {
	u.SetExcluded(entity.FieldUpdatedAt)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *EntityUpsert) SetDeletedAt(v time.Time) *EntityUpsert {
	u.Set(entity.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *EntityUpsert) UpdateDeletedAt() *EntityUpsert {
	u.SetExcluded(entity.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *EntityUpsert) ClearDeletedAt() *EntityUpsert {
	u.SetNull(entity.FieldDeletedAt)
	return u
}

// SetType sets the "type" field.
func (u *EntityUpsert) SetType(v int) *EntityUpsert {
	u.Set(entity.FieldType, v)
	return u
}

// UpdateType sets the "type" field to the value that was provided on create.
func (u *EntityUpsert) UpdateType() *EntityUpsert {
	u.SetExcluded(entity.FieldType)
	return u
}

// AddType adds v to the "type" field.
func (u *EntityUpsert) AddType(v int) *EntityUpsert {
	u.Add(entity.FieldType, v)
	return u
}

// SetSource sets the "source" field.
func (u *EntityUpsert) SetSource(v string) *EntityUpsert {
	u.Set(entity.FieldSource, v)
	return u
}

// UpdateSource sets the "source" field to the value that was provided on create.
func (u *EntityUpsert) UpdateSource() *EntityUpsert {
	u.SetExcluded(entity.FieldSource)
	return u
}

// SetSize sets the "size" field.
func (u *EntityUpsert) SetSize(v int64) *EntityUpsert {
	u.Set(entity.FieldSize, v)
	return u
}

// UpdateSize sets the "size" field to the value that was provided on create.
func (u *EntityUpsert) UpdateSize() *EntityUpsert {
	u.SetExcluded(entity.FieldSize)
	return u
}

// AddSize adds v to the "size" field.
func (u *EntityUpsert) AddSize(v int64) *EntityUpsert {
	u.Add(entity.FieldSize, v)
	return u
}

// SetReferenceCount sets the "reference_count" field.
func (u *EntityUpsert) SetReferenceCount(v int) *EntityUpsert {
	u.Set(entity.FieldReferenceCount, v)
	return u
}

// UpdateReferenceCount sets the "reference_count" field to the value that was provided on create.
func (u *EntityUpsert) UpdateReferenceCount() *EntityUpsert {
	u.SetExcluded(entity.FieldReferenceCount)
	return u
}

// AddReferenceCount adds v to the "reference_count" field.
func (u *EntityUpsert) AddReferenceCount(v int) *EntityUpsert {
	u.Add(entity.FieldReferenceCount, v)
	return u
}

// SetStoragePolicyEntities sets the "storage_policy_entities" field.
func (u *EntityUpsert) SetStoragePolicyEntities(v int) *EntityUpsert {
	u.Set(entity.FieldStoragePolicyEntities, v)
	return u
}

// UpdateStoragePolicyEntities sets the "storage_policy_entities" field to the value that was provided on create.
func (u *EntityUpsert) UpdateStoragePolicyEntities() *EntityUpsert {
	u.SetExcluded(entity.FieldStoragePolicyEntities)
	return u
}

// SetCreatedBy sets the "created_by" field.
func (u *EntityUpsert) SetCreatedBy(v int) *EntityUpsert {
	u.Set(entity.FieldCreatedBy, v)
	return u
}

// UpdateCreatedBy sets the "created_by" field to the value that was provided on create.
func (u *EntityUpsert) UpdateCreatedBy() *EntityUpsert {
	u.SetExcluded(entity.FieldCreatedBy)
	return u
}

// ClearCreatedBy clears the value of the "created_by" field.
func (u *EntityUpsert) ClearCreatedBy() *EntityUpsert {
	u.SetNull(entity.FieldCreatedBy)
	return u
}

// SetUploadSessionID sets the "upload_session_id" field.
func (u *EntityUpsert) SetUploadSessionID(v uuid.UUID) *EntityUpsert {
	u.Set(entity.FieldUploadSessionID, v)
	return u
}

// UpdateUploadSessionID sets the "upload_session_id" field to the value that was provided on create.
func (u *EntityUpsert) UpdateUploadSessionID() *EntityUpsert {
	u.SetExcluded(entity.FieldUploadSessionID)
	return u
}

// ClearUploadSessionID clears the value of the "upload_session_id" field.
func (u *EntityUpsert) ClearUploadSessionID() *EntityUpsert {
	u.SetNull(entity.FieldUploadSessionID)
	return u
}

// SetRecycleOptions sets the "recycle_options" field.
func (u *EntityUpsert) SetRecycleOptions(v *types.EntityRecycleOption) *EntityUpsert {
	u.Set(entity.FieldRecycleOptions, v)
	return u
}

// UpdateRecycleOptions sets the "recycle_options" field to the value that was provided on create.
func (u *EntityUpsert) UpdateRecycleOptions() *EntityUpsert {
	u.SetExcluded(entity.FieldRecycleOptions)
	return u
}

// ClearRecycleOptions clears the value of the "recycle_options" field.
func (u *EntityUpsert) ClearRecycleOptions() *EntityUpsert {
	u.SetNull(entity.FieldRecycleOptions)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create.
// Using this option is equivalent to using:
//
//	client.Entity.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//		).
//		Exec(ctx)
func (u *EntityUpsertOne) UpdateNewValues() *EntityUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(entity.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Entity.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *EntityUpsertOne) Ignore() *EntityUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *EntityUpsertOne) DoNothing() *EntityUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the EntityCreate.OnConflict
// documentation for more info.
func (u *EntityUpsertOne) Update(set func(*EntityUpsert)) *EntityUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&EntityUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *EntityUpsertOne) SetUpdatedAt(v time.Time) *EntityUpsertOne {
	return u.Update(func(s *EntityUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *EntityUpsertOne) UpdateUpdatedAt() *EntityUpsertOne {
	return u.Update(func(s *EntityUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *EntityUpsertOne) SetDeletedAt(v time.Time) *EntityUpsertOne {
	return u.Update(func(s *EntityUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *EntityUpsertOne) UpdateDeletedAt() *EntityUpsertOne {
	return u.Update(func(s *EntityUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *EntityUpsertOne) ClearDeletedAt() *EntityUpsertOne {
	return u.Update(func(s *EntityUpsert) {
		s.ClearDeletedAt()
	})
}

// SetType sets the "type" field.
func (u *EntityUpsertOne) SetType(v int) *EntityUpsertOne {
	return u.Update(func(s *EntityUpsert) {
		s.SetType(v)
	})
}

// AddType adds v to the "type" field.
func (u *EntityUpsertOne) AddType(v int) *EntityUpsertOne {
	return u.Update(func(s *EntityUpsert) {
		s.AddType(v)
	})
}

// UpdateType sets the "type" field to the value that was provided on create.
func (u *EntityUpsertOne) UpdateType() *EntityUpsertOne {
	return u.Update(func(s *EntityUpsert) {
		s.UpdateType()
	})
}

// SetSource sets the "source" field.
func (u *EntityUpsertOne) SetSource(v string) *EntityUpsertOne {
	return u.Update(func(s *EntityUpsert) {
		s.SetSource(v)
	})
}

// UpdateSource sets the "source" field to the value that was provided on create.
func (u *EntityUpsertOne) UpdateSource() *EntityUpsertOne {
	return u.Update(func(s *EntityUpsert) {
		s.UpdateSource()
	})
}

// SetSize sets the "size" field.
func (u *EntityUpsertOne) SetSize(v int64) *EntityUpsertOne {
	return u.Update(func(s *EntityUpsert) {
		s.SetSize(v)
	})
}

// AddSize adds v to the "size" field.
func (u *EntityUpsertOne) AddSize(v int64) *EntityUpsertOne {
	return u.Update(func(s *EntityUpsert) {
		s.AddSize(v)
	})
}

// UpdateSize sets the "size" field to the value that was provided on create.
func (u *EntityUpsertOne) UpdateSize() *EntityUpsertOne {
	return u.Update(func(s *EntityUpsert) {
		s.UpdateSize()
	})
}

// SetReferenceCount sets the "reference_count" field.
func (u *EntityUpsertOne) SetReferenceCount(v int) *EntityUpsertOne {
	return u.Update(func(s *EntityUpsert) {
		s.SetReferenceCount(v)
	})
}

// AddReferenceCount adds v to the "reference_count" field.
func (u *EntityUpsertOne) AddReferenceCount(v int) *EntityUpsertOne {
	return u.Update(func(s *EntityUpsert) {
		s.AddReferenceCount(v)
	})
}

// UpdateReferenceCount sets the "reference_count" field to the value that was provided on create.
func (u *EntityUpsertOne) UpdateReferenceCount() *EntityUpsertOne {
	return u.Update(func(s *EntityUpsert) {
		s.UpdateReferenceCount()
	})
}

// SetStoragePolicyEntities sets the "storage_policy_entities" field.
func (u *EntityUpsertOne) SetStoragePolicyEntities(v int) *EntityUpsertOne {
	return u.Update(func(s *EntityUpsert) {
		s.SetStoragePolicyEntities(v)
	})
}

// UpdateStoragePolicyEntities sets the "storage_policy_entities" field to the value that was provided on create.
func (u *EntityUpsertOne) UpdateStoragePolicyEntities() *EntityUpsertOne {
	return u.Update(func(s *EntityUpsert) {
		s.UpdateStoragePolicyEntities()
	})
}

// SetCreatedBy sets the "created_by" field.
func (u *EntityUpsertOne) SetCreatedBy(v int) *EntityUpsertOne {
	return u.Update(func(s *EntityUpsert) {
		s.SetCreatedBy(v)
	})
}

// UpdateCreatedBy sets the "created_by" field to the value that was provided on create.
func (u *EntityUpsertOne) UpdateCreatedBy() *EntityUpsertOne {
	return u.Update(func(s *EntityUpsert) {
		s.UpdateCreatedBy()
	})
}

// ClearCreatedBy clears the value of the "created_by" field.
func (u *EntityUpsertOne) ClearCreatedBy() *EntityUpsertOne {
	return u.Update(func(s *EntityUpsert) {
		s.ClearCreatedBy()
	})
}

// SetUploadSessionID sets the "upload_session_id" field.
func (u *EntityUpsertOne) SetUploadSessionID(v uuid.UUID) *EntityUpsertOne {
	return u.Update(func(s *EntityUpsert) {
		s.SetUploadSessionID(v)
	})
}

// UpdateUploadSessionID sets the "upload_session_id" field to the value that was provided on create.
func (u *EntityUpsertOne) UpdateUploadSessionID() *EntityUpsertOne {
	return u.Update(func(s *EntityUpsert) {
		s.UpdateUploadSessionID()
	})
}

// ClearUploadSessionID clears the value of the "upload_session_id" field.
func (u *EntityUpsertOne) ClearUploadSessionID() *EntityUpsertOne {
	return u.Update(func(s *EntityUpsert) {
		s.ClearUploadSessionID()
	})
}

// SetRecycleOptions sets the "recycle_options" field.
func (u *EntityUpsertOne) SetRecycleOptions(v *types.EntityRecycleOption) *EntityUpsertOne {
	return u.Update(func(s *EntityUpsert) {
		s.SetRecycleOptions(v)
	})
}

// UpdateRecycleOptions sets the "recycle_options" field to the value that was provided on create.
func (u *EntityUpsertOne) UpdateRecycleOptions() *EntityUpsertOne {
	return u.Update(func(s *EntityUpsert) {
		s.UpdateRecycleOptions()
	})
}

// ClearRecycleOptions clears the value of the "recycle_options" field.
func (u *EntityUpsertOne) ClearRecycleOptions() *EntityUpsertOne {
	return u.Update(func(s *EntityUpsert) {
		s.ClearRecycleOptions()
	})
}

// Exec executes the query.
func (u *EntityUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for EntityCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *EntityUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *EntityUpsertOne) ID(ctx context.Context) (id int, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *EntityUpsertOne) IDX(ctx context.Context) int {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

func (m *EntityCreate) SetRawID(t int) *EntityCreate {
	m.mutation.SetRawID(t)
	return m
}

// EntityCreateBulk is the builder for creating many Entity entities in bulk.
type EntityCreateBulk struct {
	config
	err      error
	builders []*EntityCreate
	conflict []sql.ConflictOption
}

// Save creates the Entity entities in the database.
func (ecb *EntityCreateBulk) Save(ctx context.Context) ([]*Entity, error) {
	if ecb.err != nil {
		return nil, ecb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(ecb.builders))
	nodes := make([]*Entity, len(ecb.builders))
	mutators := make([]Mutator, len(ecb.builders))
	for i := range ecb.builders {
		func(i int, root context.Context) {
			builder := ecb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*EntityMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, ecb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = ecb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, ecb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, ecb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (ecb *EntityCreateBulk) SaveX(ctx context.Context) []*Entity {
	v, err := ecb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ecb *EntityCreateBulk) Exec(ctx context.Context) error {
	_, err := ecb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ecb *EntityCreateBulk) ExecX(ctx context.Context) {
	if err := ecb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Entity.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.EntityUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (ecb *EntityCreateBulk) OnConflict(opts ...sql.ConflictOption) *EntityUpsertBulk {
	ecb.conflict = opts
	return &EntityUpsertBulk{
		create: ecb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Entity.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (ecb *EntityCreateBulk) OnConflictColumns(columns ...string) *EntityUpsertBulk {
	ecb.conflict = append(ecb.conflict, sql.ConflictColumns(columns...))
	return &EntityUpsertBulk{
		create: ecb,
	}
}

// EntityUpsertBulk is the builder for "upsert"-ing
// a bulk of Entity nodes.
type EntityUpsertBulk struct {
	create *EntityCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Entity.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//		).
//		Exec(ctx)
func (u *EntityUpsertBulk) UpdateNewValues() *EntityUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(entity.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Entity.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *EntityUpsertBulk) Ignore() *EntityUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *EntityUpsertBulk) DoNothing() *EntityUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the EntityCreateBulk.OnConflict
// documentation for more info.
func (u *EntityUpsertBulk) Update(set func(*EntityUpsert)) *EntityUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&EntityUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *EntityUpsertBulk) SetUpdatedAt(v time.Time) *EntityUpsertBulk {
	return u.Update(func(s *EntityUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *EntityUpsertBulk) UpdateUpdatedAt() *EntityUpsertBulk {
	return u.Update(func(s *EntityUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *EntityUpsertBulk) SetDeletedAt(v time.Time) *EntityUpsertBulk {
	return u.Update(func(s *EntityUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *EntityUpsertBulk) UpdateDeletedAt() *EntityUpsertBulk {
	return u.Update(func(s *EntityUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *EntityUpsertBulk) ClearDeletedAt() *EntityUpsertBulk {
	return u.Update(func(s *EntityUpsert) {
		s.ClearDeletedAt()
	})
}

// SetType sets the "type" field.
func (u *EntityUpsertBulk) SetType(v int) *EntityUpsertBulk {
	return u.Update(func(s *EntityUpsert) {
		s.SetType(v)
	})
}

// AddType adds v to the "type" field.
func (u *EntityUpsertBulk) AddType(v int) *EntityUpsertBulk {
	return u.Update(func(s *EntityUpsert) {
		s.AddType(v)
	})
}

// UpdateType sets the "type" field to the value that was provided on create.
func (u *EntityUpsertBulk) UpdateType() *EntityUpsertBulk {
	return u.Update(func(s *EntityUpsert) {
		s.UpdateType()
	})
}

// SetSource sets the "source" field.
func (u *EntityUpsertBulk) SetSource(v string) *EntityUpsertBulk {
	return u.Update(func(s *EntityUpsert) {
		s.SetSource(v)
	})
}

// UpdateSource sets the "source" field to the value that was provided on create.
func (u *EntityUpsertBulk) UpdateSource() *EntityUpsertBulk {
	return u.Update(func(s *EntityUpsert) {
		s.UpdateSource()
	})
}

// SetSize sets the "size" field.
func (u *EntityUpsertBulk) SetSize(v int64) *EntityUpsertBulk {
	return u.Update(func(s *EntityUpsert) {
		s.SetSize(v)
	})
}

// AddSize adds v to the "size" field.
func (u *EntityUpsertBulk) AddSize(v int64) *EntityUpsertBulk {
	return u.Update(func(s *EntityUpsert) {
		s.AddSize(v)
	})
}

// UpdateSize sets the "size" field to the value that was provided on create.
func (u *EntityUpsertBulk) UpdateSize() *EntityUpsertBulk {
	return u.Update(func(s *EntityUpsert) {
		s.UpdateSize()
	})
}

// SetReferenceCount sets the "reference_count" field.
func (u *EntityUpsertBulk) SetReferenceCount(v int) *EntityUpsertBulk {
	return u.Update(func(s *EntityUpsert) {
		s.SetReferenceCount(v)
	})
}

// AddReferenceCount adds v to the "reference_count" field.
func (u *EntityUpsertBulk) AddReferenceCount(v int) *EntityUpsertBulk {
	return u.Update(func(s *EntityUpsert) {
		s.AddReferenceCount(v)
	})
}

// UpdateReferenceCount sets the "reference_count" field to the value that was provided on create.
func (u *EntityUpsertBulk) UpdateReferenceCount() *EntityUpsertBulk {
	return u.Update(func(s *EntityUpsert) {
		s.UpdateReferenceCount()
	})
}

// SetStoragePolicyEntities sets the "storage_policy_entities" field.
func (u *EntityUpsertBulk) SetStoragePolicyEntities(v int) *EntityUpsertBulk {
	return u.Update(func(s *EntityUpsert) {
		s.SetStoragePolicyEntities(v)
	})
}

// UpdateStoragePolicyEntities sets the "storage_policy_entities" field to the value that was provided on create.
func (u *EntityUpsertBulk) UpdateStoragePolicyEntities() *EntityUpsertBulk {
	return u.Update(func(s *EntityUpsert) {
		s.UpdateStoragePolicyEntities()
	})
}

// SetCreatedBy sets the "created_by" field.
func (u *EntityUpsertBulk) SetCreatedBy(v int) *EntityUpsertBulk {
	return u.Update(func(s *EntityUpsert) {
		s.SetCreatedBy(v)
	})
}

// UpdateCreatedBy sets the "created_by" field to the value that was provided on create.
func (u *EntityUpsertBulk) UpdateCreatedBy() *EntityUpsertBulk {
	return u.Update(func(s *EntityUpsert) {
		s.UpdateCreatedBy()
	})
}

// ClearCreatedBy clears the value of the "created_by" field.
func (u *EntityUpsertBulk) ClearCreatedBy() *EntityUpsertBulk {
	return u.Update(func(s *EntityUpsert) {
		s.ClearCreatedBy()
	})
}

// SetUploadSessionID sets the "upload_session_id" field.
func (u *EntityUpsertBulk) SetUploadSessionID(v uuid.UUID) *EntityUpsertBulk {
	return u.Update(func(s *EntityUpsert) {
		s.SetUploadSessionID(v)
	})
}

// UpdateUploadSessionID sets the "upload_session_id" field to the value that was provided on create.
func (u *EntityUpsertBulk) UpdateUploadSessionID() *EntityUpsertBulk {
	return u.Update(func(s *EntityUpsert) {
		s.UpdateUploadSessionID()
	})
}

// ClearUploadSessionID clears the value of the "upload_session_id" field.
func (u *EntityUpsertBulk) ClearUploadSessionID() *EntityUpsertBulk {
	return u.Update(func(s *EntityUpsert) {
		s.ClearUploadSessionID()
	})
}

// SetRecycleOptions sets the "recycle_options" field.
func (u *EntityUpsertBulk) SetRecycleOptions(v *types.EntityRecycleOption) *EntityUpsertBulk {
	return u.Update(func(s *EntityUpsert) {
		s.SetRecycleOptions(v)
	})
}

// UpdateRecycleOptions sets the "recycle_options" field to the value that was provided on create.
func (u *EntityUpsertBulk) UpdateRecycleOptions() *EntityUpsertBulk {
	return u.Update(func(s *EntityUpsert) {
		s.UpdateRecycleOptions()
	})
}

// ClearRecycleOptions clears the value of the "recycle_options" field.
func (u *EntityUpsertBulk) ClearRecycleOptions() *EntityUpsertBulk {
	return u.Update(func(s *EntityUpsert) {
		s.ClearRecycleOptions()
	})
}

// Exec executes the query.
func (u *EntityUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the EntityCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for EntityCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *EntityUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
