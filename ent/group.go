// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/cloudreve/Cloudreve/v4/ent/group"
	"github.com/cloudreve/Cloudreve/v4/ent/storagepolicy"
	"github.com/cloudreve/Cloudreve/v4/inventory/types"
	"github.com/cloudreve/Cloudreve/v4/pkg/boolset"
)

// Group is the model entity for the Group schema.
type Group struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt *time.Time `json:"deleted_at,omitempty"`
	// Name holds the value of the "name" field.
	Name string `json:"name,omitempty"`
	// MaxStorage holds the value of the "max_storage" field.
	MaxStorage int64 `json:"max_storage,omitempty"`
	// SpeedLimit holds the value of the "speed_limit" field.
	SpeedLimit int `json:"speed_limit,omitempty"`
	// Permissions holds the value of the "permissions" field.
	Permissions *boolset.BooleanSet `json:"permissions,omitempty"`
	// Settings holds the value of the "settings" field.
	Settings *types.GroupSetting `json:"settings,omitempty"`
	// StoragePolicyID holds the value of the "storage_policy_id" field.
	StoragePolicyID int `json:"storage_policy_id,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the GroupQuery when eager-loading is set.
	Edges        GroupEdges `json:"edges"`
	selectValues sql.SelectValues
}

// GroupEdges holds the relations/edges for other nodes in the graph.
type GroupEdges struct {
	// Users holds the value of the users edge.
	Users []*User `json:"users,omitempty"`
	// StoragePolicies holds the value of the storage_policies edge.
	StoragePolicies *StoragePolicy `json:"storage_policies,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [2]bool
}

// UsersOrErr returns the Users value or an error if the edge
// was not loaded in eager-loading.
func (e GroupEdges) UsersOrErr() ([]*User, error) {
	if e.loadedTypes[0] {
		return e.Users, nil
	}
	return nil, &NotLoadedError{edge: "users"}
}

// StoragePoliciesOrErr returns the StoragePolicies value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e GroupEdges) StoragePoliciesOrErr() (*StoragePolicy, error) {
	if e.loadedTypes[1] {
		if e.StoragePolicies == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: storagepolicy.Label}
		}
		return e.StoragePolicies, nil
	}
	return nil, &NotLoadedError{edge: "storage_policies"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Group) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case group.FieldSettings:
			values[i] = new([]byte)
		case group.FieldPermissions:
			values[i] = new(boolset.BooleanSet)
		case group.FieldID, group.FieldMaxStorage, group.FieldSpeedLimit, group.FieldStoragePolicyID:
			values[i] = new(sql.NullInt64)
		case group.FieldName:
			values[i] = new(sql.NullString)
		case group.FieldCreatedAt, group.FieldUpdatedAt, group.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Group fields.
func (gr *Group) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case group.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			gr.ID = int(value.Int64)
		case group.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				gr.CreatedAt = value.Time
			}
		case group.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				gr.UpdatedAt = value.Time
			}
		case group.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				gr.DeletedAt = new(time.Time)
				*gr.DeletedAt = value.Time
			}
		case group.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				gr.Name = value.String
			}
		case group.FieldMaxStorage:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field max_storage", values[i])
			} else if value.Valid {
				gr.MaxStorage = value.Int64
			}
		case group.FieldSpeedLimit:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field speed_limit", values[i])
			} else if value.Valid {
				gr.SpeedLimit = int(value.Int64)
			}
		case group.FieldPermissions:
			if value, ok := values[i].(*boolset.BooleanSet); !ok {
				return fmt.Errorf("unexpected type %T for field permissions", values[i])
			} else if value != nil {
				gr.Permissions = value
			}
		case group.FieldSettings:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field settings", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &gr.Settings); err != nil {
					return fmt.Errorf("unmarshal field settings: %w", err)
				}
			}
		case group.FieldStoragePolicyID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field storage_policy_id", values[i])
			} else if value.Valid {
				gr.StoragePolicyID = int(value.Int64)
			}
		default:
			gr.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Group.
// This includes values selected through modifiers, order, etc.
func (gr *Group) Value(name string) (ent.Value, error) {
	return gr.selectValues.Get(name)
}

// QueryUsers queries the "users" edge of the Group entity.
func (gr *Group) QueryUsers() *UserQuery {
	return NewGroupClient(gr.config).QueryUsers(gr)
}

// QueryStoragePolicies queries the "storage_policies" edge of the Group entity.
func (gr *Group) QueryStoragePolicies() *StoragePolicyQuery {
	return NewGroupClient(gr.config).QueryStoragePolicies(gr)
}

// Update returns a builder for updating this Group.
// Note that you need to call Group.Unwrap() before calling this method if this Group
// was returned from a transaction, and the transaction was committed or rolled back.
func (gr *Group) Update() *GroupUpdateOne {
	return NewGroupClient(gr.config).UpdateOne(gr)
}

// Unwrap unwraps the Group entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (gr *Group) Unwrap() *Group {
	_tx, ok := gr.config.driver.(*txDriver)
	if !ok {
		panic("ent: Group is not a transactional entity")
	}
	gr.config.driver = _tx.drv
	return gr
}

// String implements the fmt.Stringer.
func (gr *Group) String() string {
	var builder strings.Builder
	builder.WriteString("Group(")
	builder.WriteString(fmt.Sprintf("id=%v, ", gr.ID))
	builder.WriteString("created_at=")
	builder.WriteString(gr.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(gr.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := gr.DeletedAt; v != nil {
		builder.WriteString("deleted_at=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(gr.Name)
	builder.WriteString(", ")
	builder.WriteString("max_storage=")
	builder.WriteString(fmt.Sprintf("%v", gr.MaxStorage))
	builder.WriteString(", ")
	builder.WriteString("speed_limit=")
	builder.WriteString(fmt.Sprintf("%v", gr.SpeedLimit))
	builder.WriteString(", ")
	builder.WriteString("permissions=")
	builder.WriteString(fmt.Sprintf("%v", gr.Permissions))
	builder.WriteString(", ")
	builder.WriteString("settings=")
	builder.WriteString(fmt.Sprintf("%v", gr.Settings))
	builder.WriteString(", ")
	builder.WriteString("storage_policy_id=")
	builder.WriteString(fmt.Sprintf("%v", gr.StoragePolicyID))
	builder.WriteByte(')')
	return builder.String()
}

// SetUsers manually set the edge as loaded state.
func (e *Group) SetUsers(v []*User) {
	e.Edges.Users = v
	e.Edges.loadedTypes[0] = true
}

// SetStoragePolicies manually set the edge as loaded state.
func (e *Group) SetStoragePolicies(v *StoragePolicy) {
	e.Edges.StoragePolicies = v
	e.Edges.loadedTypes[1] = true
}

// Groups is a parsable slice of Group.
type Groups []*Group
