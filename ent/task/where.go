// Code generated by ent, DO NOT EDIT.

package task

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/cloudreve/Cloudreve/v4/ent/predicate"
	"github.com/gofrs/uuid"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.Task {
	return predicate.Task(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.Task {
	return predicate.Task(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.Task {
	return predicate.Task(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.Task {
	return predicate.Task(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.Task {
	return predicate.Task(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.Task {
	return predicate.Task(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.Task {
	return predicate.Task(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.Task {
	return predicate.Task(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.Task {
	return predicate.Task(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Task {
	return predicate.Task(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Task {
	return predicate.Task(sql.FieldEQ(FieldUpdatedAt, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.Task {
	return predicate.Task(sql.FieldEQ(FieldDeletedAt, v))
}

// Type applies equality check predicate on the "type" field. It's identical to TypeEQ.
func Type(v string) predicate.Task {
	return predicate.Task(sql.FieldEQ(FieldType, v))
}

// PrivateState applies equality check predicate on the "private_state" field. It's identical to PrivateStateEQ.
func PrivateState(v string) predicate.Task {
	return predicate.Task(sql.FieldEQ(FieldPrivateState, v))
}

// CorrelationID applies equality check predicate on the "correlation_id" field. It's identical to CorrelationIDEQ.
func CorrelationID(v uuid.UUID) predicate.Task {
	return predicate.Task(sql.FieldEQ(FieldCorrelationID, v))
}

// UserTasks applies equality check predicate on the "user_tasks" field. It's identical to UserTasksEQ.
func UserTasks(v int) predicate.Task {
	return predicate.Task(sql.FieldEQ(FieldUserTasks, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Task {
	return predicate.Task(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Task {
	return predicate.Task(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Task {
	return predicate.Task(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Task {
	return predicate.Task(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Task {
	return predicate.Task(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Task {
	return predicate.Task(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Task {
	return predicate.Task(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Task {
	return predicate.Task(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Task {
	return predicate.Task(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Task {
	return predicate.Task(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Task {
	return predicate.Task(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Task {
	return predicate.Task(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Task {
	return predicate.Task(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Task {
	return predicate.Task(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Task {
	return predicate.Task(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Task {
	return predicate.Task(sql.FieldLTE(FieldUpdatedAt, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.Task {
	return predicate.Task(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.Task {
	return predicate.Task(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.Task {
	return predicate.Task(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.Task {
	return predicate.Task(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.Task {
	return predicate.Task(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.Task {
	return predicate.Task(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.Task {
	return predicate.Task(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.Task {
	return predicate.Task(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.Task {
	return predicate.Task(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.Task {
	return predicate.Task(sql.FieldNotNull(FieldDeletedAt))
}

// TypeEQ applies the EQ predicate on the "type" field.
func TypeEQ(v string) predicate.Task {
	return predicate.Task(sql.FieldEQ(FieldType, v))
}

// TypeNEQ applies the NEQ predicate on the "type" field.
func TypeNEQ(v string) predicate.Task {
	return predicate.Task(sql.FieldNEQ(FieldType, v))
}

// TypeIn applies the In predicate on the "type" field.
func TypeIn(vs ...string) predicate.Task {
	return predicate.Task(sql.FieldIn(FieldType, vs...))
}

// TypeNotIn applies the NotIn predicate on the "type" field.
func TypeNotIn(vs ...string) predicate.Task {
	return predicate.Task(sql.FieldNotIn(FieldType, vs...))
}

// TypeGT applies the GT predicate on the "type" field.
func TypeGT(v string) predicate.Task {
	return predicate.Task(sql.FieldGT(FieldType, v))
}

// TypeGTE applies the GTE predicate on the "type" field.
func TypeGTE(v string) predicate.Task {
	return predicate.Task(sql.FieldGTE(FieldType, v))
}

// TypeLT applies the LT predicate on the "type" field.
func TypeLT(v string) predicate.Task {
	return predicate.Task(sql.FieldLT(FieldType, v))
}

// TypeLTE applies the LTE predicate on the "type" field.
func TypeLTE(v string) predicate.Task {
	return predicate.Task(sql.FieldLTE(FieldType, v))
}

// TypeContains applies the Contains predicate on the "type" field.
func TypeContains(v string) predicate.Task {
	return predicate.Task(sql.FieldContains(FieldType, v))
}

// TypeHasPrefix applies the HasPrefix predicate on the "type" field.
func TypeHasPrefix(v string) predicate.Task {
	return predicate.Task(sql.FieldHasPrefix(FieldType, v))
}

// TypeHasSuffix applies the HasSuffix predicate on the "type" field.
func TypeHasSuffix(v string) predicate.Task {
	return predicate.Task(sql.FieldHasSuffix(FieldType, v))
}

// TypeEqualFold applies the EqualFold predicate on the "type" field.
func TypeEqualFold(v string) predicate.Task {
	return predicate.Task(sql.FieldEqualFold(FieldType, v))
}

// TypeContainsFold applies the ContainsFold predicate on the "type" field.
func TypeContainsFold(v string) predicate.Task {
	return predicate.Task(sql.FieldContainsFold(FieldType, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v Status) predicate.Task {
	return predicate.Task(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v Status) predicate.Task {
	return predicate.Task(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...Status) predicate.Task {
	return predicate.Task(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...Status) predicate.Task {
	return predicate.Task(sql.FieldNotIn(FieldStatus, vs...))
}

// PrivateStateEQ applies the EQ predicate on the "private_state" field.
func PrivateStateEQ(v string) predicate.Task {
	return predicate.Task(sql.FieldEQ(FieldPrivateState, v))
}

// PrivateStateNEQ applies the NEQ predicate on the "private_state" field.
func PrivateStateNEQ(v string) predicate.Task {
	return predicate.Task(sql.FieldNEQ(FieldPrivateState, v))
}

// PrivateStateIn applies the In predicate on the "private_state" field.
func PrivateStateIn(vs ...string) predicate.Task {
	return predicate.Task(sql.FieldIn(FieldPrivateState, vs...))
}

// PrivateStateNotIn applies the NotIn predicate on the "private_state" field.
func PrivateStateNotIn(vs ...string) predicate.Task {
	return predicate.Task(sql.FieldNotIn(FieldPrivateState, vs...))
}

// PrivateStateGT applies the GT predicate on the "private_state" field.
func PrivateStateGT(v string) predicate.Task {
	return predicate.Task(sql.FieldGT(FieldPrivateState, v))
}

// PrivateStateGTE applies the GTE predicate on the "private_state" field.
func PrivateStateGTE(v string) predicate.Task {
	return predicate.Task(sql.FieldGTE(FieldPrivateState, v))
}

// PrivateStateLT applies the LT predicate on the "private_state" field.
func PrivateStateLT(v string) predicate.Task {
	return predicate.Task(sql.FieldLT(FieldPrivateState, v))
}

// PrivateStateLTE applies the LTE predicate on the "private_state" field.
func PrivateStateLTE(v string) predicate.Task {
	return predicate.Task(sql.FieldLTE(FieldPrivateState, v))
}

// PrivateStateContains applies the Contains predicate on the "private_state" field.
func PrivateStateContains(v string) predicate.Task {
	return predicate.Task(sql.FieldContains(FieldPrivateState, v))
}

// PrivateStateHasPrefix applies the HasPrefix predicate on the "private_state" field.
func PrivateStateHasPrefix(v string) predicate.Task {
	return predicate.Task(sql.FieldHasPrefix(FieldPrivateState, v))
}

// PrivateStateHasSuffix applies the HasSuffix predicate on the "private_state" field.
func PrivateStateHasSuffix(v string) predicate.Task {
	return predicate.Task(sql.FieldHasSuffix(FieldPrivateState, v))
}

// PrivateStateIsNil applies the IsNil predicate on the "private_state" field.
func PrivateStateIsNil() predicate.Task {
	return predicate.Task(sql.FieldIsNull(FieldPrivateState))
}

// PrivateStateNotNil applies the NotNil predicate on the "private_state" field.
func PrivateStateNotNil() predicate.Task {
	return predicate.Task(sql.FieldNotNull(FieldPrivateState))
}

// PrivateStateEqualFold applies the EqualFold predicate on the "private_state" field.
func PrivateStateEqualFold(v string) predicate.Task {
	return predicate.Task(sql.FieldEqualFold(FieldPrivateState, v))
}

// PrivateStateContainsFold applies the ContainsFold predicate on the "private_state" field.
func PrivateStateContainsFold(v string) predicate.Task {
	return predicate.Task(sql.FieldContainsFold(FieldPrivateState, v))
}

// CorrelationIDEQ applies the EQ predicate on the "correlation_id" field.
func CorrelationIDEQ(v uuid.UUID) predicate.Task {
	return predicate.Task(sql.FieldEQ(FieldCorrelationID, v))
}

// CorrelationIDNEQ applies the NEQ predicate on the "correlation_id" field.
func CorrelationIDNEQ(v uuid.UUID) predicate.Task {
	return predicate.Task(sql.FieldNEQ(FieldCorrelationID, v))
}

// CorrelationIDIn applies the In predicate on the "correlation_id" field.
func CorrelationIDIn(vs ...uuid.UUID) predicate.Task {
	return predicate.Task(sql.FieldIn(FieldCorrelationID, vs...))
}

// CorrelationIDNotIn applies the NotIn predicate on the "correlation_id" field.
func CorrelationIDNotIn(vs ...uuid.UUID) predicate.Task {
	return predicate.Task(sql.FieldNotIn(FieldCorrelationID, vs...))
}

// CorrelationIDGT applies the GT predicate on the "correlation_id" field.
func CorrelationIDGT(v uuid.UUID) predicate.Task {
	return predicate.Task(sql.FieldGT(FieldCorrelationID, v))
}

// CorrelationIDGTE applies the GTE predicate on the "correlation_id" field.
func CorrelationIDGTE(v uuid.UUID) predicate.Task {
	return predicate.Task(sql.FieldGTE(FieldCorrelationID, v))
}

// CorrelationIDLT applies the LT predicate on the "correlation_id" field.
func CorrelationIDLT(v uuid.UUID) predicate.Task {
	return predicate.Task(sql.FieldLT(FieldCorrelationID, v))
}

// CorrelationIDLTE applies the LTE predicate on the "correlation_id" field.
func CorrelationIDLTE(v uuid.UUID) predicate.Task {
	return predicate.Task(sql.FieldLTE(FieldCorrelationID, v))
}

// CorrelationIDIsNil applies the IsNil predicate on the "correlation_id" field.
func CorrelationIDIsNil() predicate.Task {
	return predicate.Task(sql.FieldIsNull(FieldCorrelationID))
}

// CorrelationIDNotNil applies the NotNil predicate on the "correlation_id" field.
func CorrelationIDNotNil() predicate.Task {
	return predicate.Task(sql.FieldNotNull(FieldCorrelationID))
}

// UserTasksEQ applies the EQ predicate on the "user_tasks" field.
func UserTasksEQ(v int) predicate.Task {
	return predicate.Task(sql.FieldEQ(FieldUserTasks, v))
}

// UserTasksNEQ applies the NEQ predicate on the "user_tasks" field.
func UserTasksNEQ(v int) predicate.Task {
	return predicate.Task(sql.FieldNEQ(FieldUserTasks, v))
}

// UserTasksIn applies the In predicate on the "user_tasks" field.
func UserTasksIn(vs ...int) predicate.Task {
	return predicate.Task(sql.FieldIn(FieldUserTasks, vs...))
}

// UserTasksNotIn applies the NotIn predicate on the "user_tasks" field.
func UserTasksNotIn(vs ...int) predicate.Task {
	return predicate.Task(sql.FieldNotIn(FieldUserTasks, vs...))
}

// UserTasksIsNil applies the IsNil predicate on the "user_tasks" field.
func UserTasksIsNil() predicate.Task {
	return predicate.Task(sql.FieldIsNull(FieldUserTasks))
}

// UserTasksNotNil applies the NotNil predicate on the "user_tasks" field.
func UserTasksNotNil() predicate.Task {
	return predicate.Task(sql.FieldNotNull(FieldUserTasks))
}

// HasUser applies the HasEdge predicate on the "user" edge.
func HasUser() predicate.Task {
	return predicate.Task(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, UserTable, UserColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasUserWith applies the HasEdge predicate on the "user" edge with a given conditions (other predicates).
func HasUserWith(preds ...predicate.User) predicate.Task {
	return predicate.Task(func(s *sql.Selector) {
		step := newUserStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Task) predicate.Task {
	return predicate.Task(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Task) predicate.Task {
	return predicate.Task(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Task) predicate.Task {
	return predicate.Task(sql.NotPredicates(p))
}
