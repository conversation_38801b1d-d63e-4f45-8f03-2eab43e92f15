// Code generated by ent, DO NOT EDIT.

package file

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the file type in the database.
	Label = "file"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// FieldType holds the string denoting the type field in the database.
	FieldType = "type"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldOwnerID holds the string denoting the owner_id field in the database.
	FieldOwnerID = "owner_id"
	// FieldSize holds the string denoting the size field in the database.
	FieldSize = "size"
	// FieldPrimaryEntity holds the string denoting the primary_entity field in the database.
	FieldPrimaryEntity = "primary_entity"
	// FieldFileChildren holds the string denoting the file_children field in the database.
	FieldFileChildren = "file_children"
	// FieldIsSymbolic holds the string denoting the is_symbolic field in the database.
	FieldIsSymbolic = "is_symbolic"
	// FieldProps holds the string denoting the props field in the database.
	FieldProps = "props"
	// FieldStoragePolicyFiles holds the string denoting the storage_policy_files field in the database.
	FieldStoragePolicyFiles = "storage_policy_files"
	// EdgeOwner holds the string denoting the owner edge name in mutations.
	EdgeOwner = "owner"
	// EdgeStoragePolicies holds the string denoting the storage_policies edge name in mutations.
	EdgeStoragePolicies = "storage_policies"
	// EdgeParent holds the string denoting the parent edge name in mutations.
	EdgeParent = "parent"
	// EdgeChildren holds the string denoting the children edge name in mutations.
	EdgeChildren = "children"
	// EdgeMetadata holds the string denoting the metadata edge name in mutations.
	EdgeMetadata = "metadata"
	// EdgeEntities holds the string denoting the entities edge name in mutations.
	EdgeEntities = "entities"
	// EdgeShares holds the string denoting the shares edge name in mutations.
	EdgeShares = "shares"
	// EdgeDirectLinks holds the string denoting the direct_links edge name in mutations.
	EdgeDirectLinks = "direct_links"
	// Table holds the table name of the file in the database.
	Table = "files"
	// OwnerTable is the table that holds the owner relation/edge.
	OwnerTable = "files"
	// OwnerInverseTable is the table name for the User entity.
	// It exists in this package in order to avoid circular dependency with the "user" package.
	OwnerInverseTable = "users"
	// OwnerColumn is the table column denoting the owner relation/edge.
	OwnerColumn = "owner_id"
	// StoragePoliciesTable is the table that holds the storage_policies relation/edge.
	StoragePoliciesTable = "files"
	// StoragePoliciesInverseTable is the table name for the StoragePolicy entity.
	// It exists in this package in order to avoid circular dependency with the "storagepolicy" package.
	StoragePoliciesInverseTable = "storage_policies"
	// StoragePoliciesColumn is the table column denoting the storage_policies relation/edge.
	StoragePoliciesColumn = "storage_policy_files"
	// ParentTable is the table that holds the parent relation/edge.
	ParentTable = "files"
	// ParentColumn is the table column denoting the parent relation/edge.
	ParentColumn = "file_children"
	// ChildrenTable is the table that holds the children relation/edge.
	ChildrenTable = "files"
	// ChildrenColumn is the table column denoting the children relation/edge.
	ChildrenColumn = "file_children"
	// MetadataTable is the table that holds the metadata relation/edge.
	MetadataTable = "metadata"
	// MetadataInverseTable is the table name for the Metadata entity.
	// It exists in this package in order to avoid circular dependency with the "metadata" package.
	MetadataInverseTable = "metadata"
	// MetadataColumn is the table column denoting the metadata relation/edge.
	MetadataColumn = "file_id"
	// EntitiesTable is the table that holds the entities relation/edge. The primary key declared below.
	EntitiesTable = "file_entities"
	// EntitiesInverseTable is the table name for the Entity entity.
	// It exists in this package in order to avoid circular dependency with the "entity" package.
	EntitiesInverseTable = "entities"
	// SharesTable is the table that holds the shares relation/edge.
	SharesTable = "shares"
	// SharesInverseTable is the table name for the Share entity.
	// It exists in this package in order to avoid circular dependency with the "share" package.
	SharesInverseTable = "shares"
	// SharesColumn is the table column denoting the shares relation/edge.
	SharesColumn = "file_shares"
	// DirectLinksTable is the table that holds the direct_links relation/edge.
	DirectLinksTable = "direct_links"
	// DirectLinksInverseTable is the table name for the DirectLink entity.
	// It exists in this package in order to avoid circular dependency with the "directlink" package.
	DirectLinksInverseTable = "direct_links"
	// DirectLinksColumn is the table column denoting the direct_links relation/edge.
	DirectLinksColumn = "file_id"
)

// Columns holds all SQL columns for file fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldDeletedAt,
	FieldType,
	FieldName,
	FieldOwnerID,
	FieldSize,
	FieldPrimaryEntity,
	FieldFileChildren,
	FieldIsSymbolic,
	FieldProps,
	FieldStoragePolicyFiles,
}

var (
	// EntitiesPrimaryKey and EntitiesColumn2 are the table columns denoting the
	// primary key for the entities relation (M2M).
	EntitiesPrimaryKey = []string{"file_id", "entity_id"}
)

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "github.com/cloudreve/Cloudreve/v4/ent/runtime"
var (
	Hooks        [1]ent.Hook
	Interceptors [1]ent.Interceptor
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultSize holds the default value on creation for the "size" field.
	DefaultSize int64
	// DefaultIsSymbolic holds the default value on creation for the "is_symbolic" field.
	DefaultIsSymbolic bool
)

// OrderOption defines the ordering options for the File queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

// ByType orders the results by the type field.
func ByType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldType, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByOwnerID orders the results by the owner_id field.
func ByOwnerID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOwnerID, opts...).ToFunc()
}

// BySize orders the results by the size field.
func BySize(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSize, opts...).ToFunc()
}

// ByPrimaryEntity orders the results by the primary_entity field.
func ByPrimaryEntity(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPrimaryEntity, opts...).ToFunc()
}

// ByFileChildren orders the results by the file_children field.
func ByFileChildren(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFileChildren, opts...).ToFunc()
}

// ByIsSymbolic orders the results by the is_symbolic field.
func ByIsSymbolic(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsSymbolic, opts...).ToFunc()
}

// ByStoragePolicyFiles orders the results by the storage_policy_files field.
func ByStoragePolicyFiles(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStoragePolicyFiles, opts...).ToFunc()
}

// ByOwnerField orders the results by owner field.
func ByOwnerField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newOwnerStep(), sql.OrderByField(field, opts...))
	}
}

// ByStoragePoliciesField orders the results by storage_policies field.
func ByStoragePoliciesField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newStoragePoliciesStep(), sql.OrderByField(field, opts...))
	}
}

// ByParentField orders the results by parent field.
func ByParentField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newParentStep(), sql.OrderByField(field, opts...))
	}
}

// ByChildrenCount orders the results by children count.
func ByChildrenCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newChildrenStep(), opts...)
	}
}

// ByChildren orders the results by children terms.
func ByChildren(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newChildrenStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByMetadataCount orders the results by metadata count.
func ByMetadataCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newMetadataStep(), opts...)
	}
}

// ByMetadata orders the results by metadata terms.
func ByMetadata(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newMetadataStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByEntitiesCount orders the results by entities count.
func ByEntitiesCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newEntitiesStep(), opts...)
	}
}

// ByEntities orders the results by entities terms.
func ByEntities(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newEntitiesStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// BySharesCount orders the results by shares count.
func BySharesCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newSharesStep(), opts...)
	}
}

// ByShares orders the results by shares terms.
func ByShares(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newSharesStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByDirectLinksCount orders the results by direct_links count.
func ByDirectLinksCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newDirectLinksStep(), opts...)
	}
}

// ByDirectLinks orders the results by direct_links terms.
func ByDirectLinks(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newDirectLinksStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}
func newOwnerStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(OwnerInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, OwnerTable, OwnerColumn),
	)
}
func newStoragePoliciesStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(StoragePoliciesInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, StoragePoliciesTable, StoragePoliciesColumn),
	)
}
func newParentStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(Table, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, ParentTable, ParentColumn),
	)
}
func newChildrenStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(Table, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, ChildrenTable, ChildrenColumn),
	)
}
func newMetadataStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(MetadataInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, MetadataTable, MetadataColumn),
	)
}
func newEntitiesStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(EntitiesInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2M, false, EntitiesTable, EntitiesPrimaryKey...),
	)
}
func newSharesStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(SharesInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, SharesTable, SharesColumn),
	)
}
func newDirectLinksStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(DirectLinksInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, DirectLinksTable, DirectLinksColumn),
	)
}
