// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/cloudreve/Cloudreve/v4/ent/davaccount"
	"github.com/cloudreve/Cloudreve/v4/ent/predicate"
)

// DavAccountDelete is the builder for deleting a DavAccount entity.
type DavAccountDelete struct {
	config
	hooks    []Hook
	mutation *DavAccountMutation
}

// Where appends a list predicates to the DavAccountDelete builder.
func (dad *DavAccountDelete) Where(ps ...predicate.DavAccount) *DavAccountDelete {
	dad.mutation.Where(ps...)
	return dad
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (dad *DavAccountDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, dad.sqlExec, dad.mutation, dad.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (dad *DavAccountDelete) ExecX(ctx context.Context) int {
	n, err := dad.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (dad *DavAccountDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(davaccount.Table, sqlgraph.NewFieldSpec(davaccount.FieldID, field.TypeInt))
	if ps := dad.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, dad.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	dad.mutation.done = true
	return affected, err
}

// DavAccountDeleteOne is the builder for deleting a single DavAccount entity.
type DavAccountDeleteOne struct {
	dad *DavAccountDelete
}

// Where appends a list predicates to the DavAccountDelete builder.
func (dado *DavAccountDeleteOne) Where(ps ...predicate.DavAccount) *DavAccountDeleteOne {
	dado.dad.mutation.Where(ps...)
	return dado
}

// Exec executes the deletion query.
func (dado *DavAccountDeleteOne) Exec(ctx context.Context) error {
	n, err := dado.dad.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{davaccount.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (dado *DavAccountDeleteOne) ExecX(ctx context.Context) {
	if err := dado.Exec(ctx); err != nil {
		panic(err)
	}
}
