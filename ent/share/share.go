// Code generated by ent, DO NOT EDIT.

package share

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the share type in the database.
	Label = "share"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// FieldPassword holds the string denoting the password field in the database.
	FieldPassword = "password"
	// FieldViews holds the string denoting the views field in the database.
	FieldViews = "views"
	// FieldDownloads holds the string denoting the downloads field in the database.
	FieldDownloads = "downloads"
	// FieldExpires holds the string denoting the expires field in the database.
	FieldExpires = "expires"
	// FieldRemainDownloads holds the string denoting the remain_downloads field in the database.
	FieldRemainDownloads = "remain_downloads"
	// FieldProps holds the string denoting the props field in the database.
	FieldProps = "props"
	// EdgeUser holds the string denoting the user edge name in mutations.
	EdgeUser = "user"
	// EdgeFile holds the string denoting the file edge name in mutations.
	EdgeFile = "file"
	// Table holds the table name of the share in the database.
	Table = "shares"
	// UserTable is the table that holds the user relation/edge.
	UserTable = "shares"
	// UserInverseTable is the table name for the User entity.
	// It exists in this package in order to avoid circular dependency with the "user" package.
	UserInverseTable = "users"
	// UserColumn is the table column denoting the user relation/edge.
	UserColumn = "user_shares"
	// FileTable is the table that holds the file relation/edge.
	FileTable = "shares"
	// FileInverseTable is the table name for the File entity.
	// It exists in this package in order to avoid circular dependency with the "file" package.
	FileInverseTable = "files"
	// FileColumn is the table column denoting the file relation/edge.
	FileColumn = "file_shares"
)

// Columns holds all SQL columns for share fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldDeletedAt,
	FieldPassword,
	FieldViews,
	FieldDownloads,
	FieldExpires,
	FieldRemainDownloads,
	FieldProps,
}

// ForeignKeys holds the SQL foreign-keys that are owned by the "shares"
// table and are not defined as standalone fields in the schema.
var ForeignKeys = []string{
	"file_shares",
	"user_shares",
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	for i := range ForeignKeys {
		if column == ForeignKeys[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "github.com/cloudreve/Cloudreve/v4/ent/runtime"
var (
	Hooks        [1]ent.Hook
	Interceptors [1]ent.Interceptor
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultViews holds the default value on creation for the "views" field.
	DefaultViews int
	// DefaultDownloads holds the default value on creation for the "downloads" field.
	DefaultDownloads int
)

// OrderOption defines the ordering options for the Share queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

// ByPassword orders the results by the password field.
func ByPassword(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPassword, opts...).ToFunc()
}

// ByViews orders the results by the views field.
func ByViews(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldViews, opts...).ToFunc()
}

// ByDownloads orders the results by the downloads field.
func ByDownloads(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDownloads, opts...).ToFunc()
}

// ByExpires orders the results by the expires field.
func ByExpires(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldExpires, opts...).ToFunc()
}

// ByRemainDownloads orders the results by the remain_downloads field.
func ByRemainDownloads(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRemainDownloads, opts...).ToFunc()
}

// ByUserField orders the results by user field.
func ByUserField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newUserStep(), sql.OrderByField(field, opts...))
	}
}

// ByFileField orders the results by file field.
func ByFileField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newFileStep(), sql.OrderByField(field, opts...))
	}
}
func newUserStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(UserInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, UserTable, UserColumn),
	)
}
func newFileStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(FileInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, FileTable, FileColumn),
	)
}
