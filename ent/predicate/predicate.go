// Code generated by ent, DO NOT EDIT.

package predicate

import (
	"entgo.io/ent/dialect/sql"
)

// DavAccount is the predicate function for davaccount builders.
type DavAccount func(*sql.Selector)

// DirectLink is the predicate function for directlink builders.
type DirectLink func(*sql.Selector)

// Entity is the predicate function for entity builders.
type Entity func(*sql.Selector)

// File is the predicate function for file builders.
type File func(*sql.Selector)

// Group is the predicate function for group builders.
type Group func(*sql.Selector)

// Metadata is the predicate function for metadata builders.
type Metadata func(*sql.Selector)

// Node is the predicate function for node builders.
type Node func(*sql.Selector)

// Passkey is the predicate function for passkey builders.
type Passkey func(*sql.Selector)

// Setting is the predicate function for setting builders.
type Setting func(*sql.Selector)

// Share is the predicate function for share builders.
type Share func(*sql.Selector)

// StoragePolicy is the predicate function for storagepolicy builders.
type StoragePolicy func(*sql.Selector)

// Task is the predicate function for task builders.
type Task func(*sql.Selector)

// User is the predicate function for user builders.
type User func(*sql.Selector)
