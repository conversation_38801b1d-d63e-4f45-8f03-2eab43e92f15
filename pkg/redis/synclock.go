package redis

import (
	"math/rand"
	"time"
	"x-short-server/pkg/logger"

	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
)

var syncRedisInstance *redsync.Redsync

const (
	minRetryDelayMilliSec = 50
	maxRetryDelayMilliSec = 75
)

func delayFunc(tries int) time.Duration {
	return time.Duration(rand.Intn(maxRetryDelayMilliSec-minRetryDelayMilliSec)+minRetryDelayMilliSec) * time.Millisecond
}

func InitSyncLocker(cfg Config) {
	pool := goredis.NewPool(RedisClient)
	syncRedisInstance = redsync.New(pool)
}

func AcquireSyncLock(name string, expiry time.Duration) *redsync.Mutex {
	return syncRedisInstance.NewMutex(name, redsync.WithTries(200), redsync.WithExpiry(expiry), redsync.WithRetryDelayFunc(delayFunc))
}

func ReleaseSyncLock(mutex *redsync.Mutex) {
	if ok, err := mutex.Unlock(); !ok || err != nil {
		logger.WithError(err).Error("ReleaseSyncLock error")
	}
}
