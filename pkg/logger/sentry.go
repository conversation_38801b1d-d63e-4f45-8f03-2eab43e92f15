package logger

import (
	"log"
	"os"
	"time"

	"github.com/getsentry/sentry-go"
)

type sentryWriter struct{}

func InitSentry() {
	dsn := os.Getenv("SENTRY_DSN")
	if dsn == "" {
		return
	}
	err := sentry.Init(sentry.ClientOptions{
		Dsn: dsn,
	})
	if err != nil {
		log.Fatalf("sentry.Init: %s", err)
		return
	}
}

func (s *sentryWriter) Write(p []byte) (n int, err error) {
	sentry.CaptureEvent(&sentry.Event{
		Message:     string(p),
		Environment: os.Getenv("ENV"),
	})
	return len(p), nil
}

func (s *sentryWriter) Sync() error {
	sentry.Flush(time.Second * 2)
	return nil
}
