package feishu

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"go.uber.org/zap"

	"store-dashboard-api/pkg/logger"
)

// Client 飞书机器人客户端
type Client struct {
	httpClient *http.Client
}

// NewClient 创建飞书客户端
func NewClient() *Client {
	return &Client{
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// MessageRequest 飞书消息请求结构
type MessageRequest struct {
	MsgType string      `json:"msg_type"`
	Content interface{} `json:"content"`
}

// TextContent 文本消息内容
type TextContent struct {
	Text string `json:"text"`
}

// SendTextMessage 发送文本消息到飞书群
func (c *Client) SendTextMessage(webhookURL, message string) error {
	// 构建请求体
	req := MessageRequest{
		MsgType: "text",
		Content: TextContent{
			Text: message,
		},
	}

	// 序列化为JSON
	jsonData, err := json.Marshal(req)
	if err != nil {
		logger.Error("Failed to marshal feishu message", zap.Error(err))
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	// 发送HTTP请求
	resp, err := c.httpClient.Post(webhookURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		logger.Error("Failed to send feishu message", zap.Error(err), zap.String("webhook_url", webhookURL))
		return fmt.Errorf("failed to send message: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		logger.Error("Feishu API returned non-200 status", 
			zap.Int("status_code", resp.StatusCode),
			zap.String("webhook_url", webhookURL))
		return fmt.Errorf("feishu API returned status %d", resp.StatusCode)
	}

	logger.Info("Feishu message sent successfully", zap.String("webhook_url", webhookURL))
	return nil
}

// OrderNotificationData 订单通知数据
type OrderNotificationData struct {
	OrderID          string  `json:"order_id"`
	OrderNumber      string  `json:"order_number"`
	OrderAmount      float64 `json:"order_amount"`
	Currency         string  `json:"currency"`
	OrderTime        string  `json:"order_time"`
	ReceivedTime     string  `json:"received_time"`
	OrderStatus      string  `json:"order_status"`
	CurrentDate      string  `json:"current_date"`
	TargetTimezone   string  `json:"target_timezone"`
	TotalOrders      int64   `json:"total_orders"`
	TotalGMV         float64 `json:"total_gmv"`
	CurrencyStats    map[string]float64 `json:"currency_stats"`
	AvgOrderValue    float64 `json:"avg_order_value"`
}

// BuildOrderNotificationMessage 构建订单通知消息
func BuildOrderNotificationMessage(data OrderNotificationData) string {
	message := fmt.Sprintf(`🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉
🆕 检测到新订单! (%s)
🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉
📦 订单ID: %s
🔢 订单号: %s
💰 订单金额: %s%.2f
📅 订单时间: %s
📧 邮件接收时间: %s
✅ 订单状态: %s

📊📊📊📊📊📊📊📊📊📊
📈 累积GMV统计 (%s)
🌏 目标时区: %s
📅 当前日期: %s (%s)
📊📊📊📊📊📊📊📊📊📊
📦 总订单数: %d
💵 总GMV: %.2f

按货币统计:`,
		data.CurrentDate,
		data.OrderID,
		data.OrderNumber,
		getCurrencySymbol(data.Currency),
		data.OrderAmount,
		data.OrderTime,
		data.ReceivedTime,
		data.OrderStatus,
		data.CurrentDate,
		data.TargetTimezone,
		data.CurrentDate,
		data.TargetTimezone,
		data.TotalOrders,
		data.TotalGMV,
	)

	// 添加货币统计
	for currency, amount := range data.CurrencyStats {
		message += fmt.Sprintf("\n  💰 %s: %.2f", getCurrencySymbol(currency), amount)
	}

	// 添加平均订单价值
	message += fmt.Sprintf("\n\n📊 平均订单价值: %.2f\n📊📊📊📊📊📊📊📊📊📊", data.AvgOrderValue)

	return message
}

// getCurrencySymbol 获取货币符号
func getCurrencySymbol(currency string) string {
	symbols := map[string]string{
		"USD": "$",
		"EUR": "€",
		"GBP": "£",
		"JPY": "¥",
		"CNY": "¥",
		"CAD": "C$",
		"AUD": "A$",
	}

	if symbol, exists := symbols[currency]; exists {
		return symbol
	}
	return currency + " "
}
