package xerr

import (
	"errors"
	"strings"
	"x-short-server/pkg/redis"

	"gorm.io/gorm"
)

func IsGormRecordNotFound(err error) bool {
	if err != nil && err == gorm.ErrRecordNotFound {
		return true
	}
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return true
	}
	return err != nil && err.Error() == "record not found"
}

func IsGinBrokenPipe(err error) bool {
	if err == nil {
		return false
	}
	return strings.Contains(strings.ToLower(err.Error()), "broken pipe")
}

func IsGinResetByPeer(err error) bool {
	if err == nil {
		return false
	}
	return strings.Contains(strings.ToLower(err.Error()), "connection reset by peer")
}

func IsGinCookieNotFound(err error) bool {
	if err == nil {
		return false
	}
	return strings.Contains(err.<PERSON><PERSON>(), "named cookie not present")
}

func IsRedisNotFound(err error) bool {
	if err == nil {
		return false
	}
	return redis.IsNil(err)
}
