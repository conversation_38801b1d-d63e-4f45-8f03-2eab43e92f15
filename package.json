{"name": "cloudreve-frontend", "private": true, "version": "4.0.0-next", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build-prod": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fontsource/roboto": "^5.0.8", "@giscus/react": "^3.1.0", "@marsidev/react-turnstile": "^1.1.0", "@mdxeditor/editor": "^3.4.0", "@mui/icons-material": "^6.0.0", "@mui/lab": "^6.0.0-beta.30", "@mui/material": "^6.4.6", "@mui/x-date-pickers": "^6.20.2", "@mui/x-tree-view": "^6.17.0", "@reduxjs/toolkit": "^2.0.1", "@types/path-browserify": "^1.0.2", "@types/streamsaver": "^2.0.4", "@uiw/color-convert": "^2.1.1", "@uiw/react-color-sketch": "^2.1.1", "artplayer": "5.2.2", "artplayer-plugin-chapter": "^1.0.0", "artplayer-plugin-hls-control": "^1.0.1", "axios": "^1.6.2", "dayjs": "^1.11.10", "fuse.js": "^7.0.0", "hls.js": "^1.6.2", "i18next": "^23.7.11", "i18next-browser-languagedetector": "^7.2.0", "i18next-chained-backend": "^4.6.2", "i18next-http-backend": "^2.4.2", "i18next-localstorage-backend": "^4.2.0", "leaflet": "^1.9.4", "lodash": "^4.17.21", "material-ui-popup-state": "^5.0.10", "monaco-editor": "^0.49.0", "mpegts.js": "^1.8.0", "mui-one-time-password-input": "^2.0.1", "notistack": "^3.0.1", "nuqs": "^2.3.1", "path-browserify": "^1.0.1", "pdfjs-dist": "4.10.38", "qrcode.react": "^4.1.0", "react": "^18.2.0", "react-animate-height": "^3.2.3", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-draggable": "^4.4.6", "react-filerobot-image-editor": "^4.8.1", "react-google-recaptcha": "^3.1.0", "react-highlight-words": "^0.20.0", "react-hotkeys-hook": "^4.5.1", "react-i18next": "^14.0.0", "react-image-crop": "^11.0.7", "react-intersection-observer": "^9.5.3", "react-konva": "^18.2.10", "react-leaflet": "^4.2.1", "react-reader": "^2.0.10", "react-redux": "^9.0.4", "react-router-dom": "^6.21.0", "react-transition-group": "^4.4.5", "react-virtuoso": "^4.10.4", "recharts": "^2.15.1", "redux": "^5.0.0", "streamsaver": "^2.0.6", "styled-components": "^6.1.11", "timeago-react": "^3.0.6"}, "devDependencies": {"@types/leaflet": "^1.9.12", "@types/lodash": "^4.14.202", "@types/node": "^20.10.5", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/webappsec-credential-management": "^0.6.9", "@types/wicg-file-system-access": "^2023.10.5", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "less": "^4.2.0", "prettier": "3.1.1", "typescript": "^5.2.2", "vite": "5.4.6", "vite-plugin-mkcert": "^1.17.6", "vite-plugin-pwa": "^0.21.1", "vite-plugin-static-copy": "^2.2.0"}}