package migration

import (
	"time"

	"github.com/go-gormigrate/gormigrate/v2"
	"gorm.io/gorm"
)

type CreateTableAdminFbConvApi2503031138 struct {
	Id          uint64    `gorm:"primary_key;autoIncrement:false;not null;default:0;comment:主键" json:"id"`
	PixelId     string    `gorm:"type:varchar(255);not null;default:'';comment:Pixel ID" json:"pixel_id"`
	AccessToken string    `gorm:"type:varchar(255);not null;default:'';comment:Access Token" json:"access_token"`
	Enabled     bool      `gorm:"type:boolean;not null;default:false;comment:是否启用" json:"enabled"`
	CreatedAt   time.Time `gorm:"type:timestamp;not null;default:current_timestamp;comment:创建时间" json:"created_at"`
	UpdatedAt   time.Time `gorm:"type:timestamp;not null;default:current_timestamp on update current_timestamp;comment:更新时间" json:"updated_at"`
}

func (CreateTableAdminFbConvApi2503031138) TableName() string {
	return "admin_fb_conv_api"
}

func init() {
	Migrations = append(Migrations, &gormigrate.Migration{
		ID: "250303_1138_create_table_admin_fb_conv_api",
		Migrate: func(tx *gorm.DB) error {
			return tx.Debug().AutoMigrate(&CreateTableAdminFbConvApi2503031138{})
		},
		Rollback: func(tx *gorm.DB) error {
			return tx.Migrator().DropTable(&CreateTableAdminFbConvApi2503031138{})
		},
	})
}
