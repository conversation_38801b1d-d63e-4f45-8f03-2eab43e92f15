package migration

import (
	"time"

	"github.com/go-gormigrate/gormigrate/v2"
	"gorm.io/gorm"
)

type CreateTablePaymentFingerprint2503291902 struct {
	Id          uint64    `gorm:"primaryKey;autoIncrement;not null;comment:ID" json:"id"`
	Fingerprint string    `gorm:"type:varchar(180);index:idx_fgpt;not null;default:'';comment:指纹" json:"fingerprint"`
	PaidAt      time.Time `gorm:"type:timestamp;not null;default:current_timestamp;comment:支付时间" json:"paid_at"`
	OrderId     uint64    `gorm:"type:bigint;index:idx_fgpt_order_id;not null;default:0;comment:订单ID" json:"order_id"`
	UserId      uint64    `gorm:"type:bigint;index:idx_fgpt_user_id;not null;default:0;comment:用户ID" json:"user_id"`
	Website     string    `gorm:"type:varchar(180);not null;default:'';comment:网站" json:"website"`
	CreatedAt   time.Time `gorm:"type:timestamp;not null;default:current_timestamp;comment:创建时间" json:"created_at"`
	UpdatedAt   time.Time `gorm:"type:timestamp;not null;default:current_timestamp on update current_timestamp;comment:更新时间" json:"updated_at"`
}

func (CreateTablePaymentFingerprint2503291902) TableName() string {
	return "payment_fingerprint"
}

func init() {
	Migrations = append(Migrations, &gormigrate.Migration{
		ID: "250329_1902_create_table_payment_fingerprint",
		Migrate: func(tx *gorm.DB) error {
			return tx.Debug().AutoMigrate(&CreateTablePaymentFingerprint2503291902{})
		},
		Rollback: func(tx *gorm.DB) error {
			return nil
		},
	})
}
