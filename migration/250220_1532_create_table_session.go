package migration

import (
	"time"

	"github.com/go-gormigrate/gormigrate/v2"
	"gorm.io/gorm"
)

type CreateTableSession2502201532 struct {
	Id        uint64    `gorm:"primary_key;autoIncrement:false;not null;default:0;comment:主键" json:"id"`
	SessionId string    `gorm:"column:session_id;type:varchar(255);not null;default:'';comment:会话ID" json:"session_id"`
	UserId    uint64    `gorm:"column:user_id;type:bigint;not null;default:0;comment:用户ID" json:"user_id"`
	UserAgent string    `gorm:"column:user_agent;type:text;comment:User-Agent" json:"user_agent"`
	Ip        string    `gorm:"column:ip;type:varchar(255);not null;default:'';comment:IP" json:"ip"`
	CreatedAt time.Time `gorm:"type:timestamp;not null;default:current_timestamp;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time `gorm:"type:timestamp;not null;default:current_timestamp on update current_timestamp;comment:更新时间" json:"updated_at"`
}

func (CreateTableSession2502201532) TableName() string {
	return "sessions"
}

func init() {
	Migrations = append(Migrations, &gormigrate.Migration{
		ID: "250220_1532_create_table_sessions",
		Migrate: func(tx *gorm.DB) error {
			return tx.Debug().AutoMigrate(&CreateTableSession2502201532{})
		},
		Rollback: func(tx *gorm.DB) error {
			return tx.Migrator().DropTable(&CreateTableSession2502201532{})
		},
	})
}
