package migration

import (
	"github.com/go-gormigrate/gormigrate/v2"
	"gorm.io/gorm"
)

type AddWebsiteOnPixel2505151041 struct {
	Website string `json:"website" gorm:"column:website;type:varchar(150);not null;default:'';comment:网站"`
}

func (AddWebsiteOnPixel2505151041) TableName() string {
	return "admin_fb_conv_api"
}

type AddWebsiteOnSystemConfig2505151041 struct {
	Website string `json:"website" gorm:"column:website;type:varchar(150);not null;default:'';comment:网站"`
}

func (AddWebsiteOnSystemConfig2505151041) TableName() string {
	return "system_config"
}

func init() {
	Migrations = append(Migrations, &gormigrate.Migration{
		ID: "20250515_1041_add_website_on_pixel",
		Migrate: func(tx *gorm.DB) error {
			return tx.Debug().AutoMigrate(&AddWebsiteOnPixel2505151041{}, &AddWebsiteOnSystemConfig2505151041{})
		},
		Rollback: func(tx *gorm.DB) error {
			return nil
		},
	})
}
