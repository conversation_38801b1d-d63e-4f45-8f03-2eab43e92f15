package migration

import (
	"time"

	"github.com/go-gormigrate/gormigrate/v2"
	"gorm.io/gorm"
)

type CreateTableUserSubscriptionLink2502201540 struct {
	Id               uint64    `gorm:"primary_key;autoIncrement:false;not null;default:0;comment:主键" json:"id"`
	PaymentAccountId uint64    `gorm:"type:bigint;not null;default:0;comment:支付账号ID" json:"payment_account_id"`
	Uid              uint64    `gorm:"type:bigint;not null;default:0;index:idx_usl_uid;comment:用户ID" json:"uid"`
	SubscriptionId   uint64    `gorm:"type:bigint;not null;default:0;comment:订阅ID" json:"subscription_id"`
	Status           string    `gorm:"type:varchar(255);not null;default:'';index:idx_usl_stat;comment:状态" json:"status"`
	LastPaidAt       time.Time `gorm:"type:timestamp;comment:最后支付时间" json:"last_paid_at"`
	NextPaidAt       time.Time `gorm:"type:timestamp;comment:下次支付时间" json:"next_paid_at"`
	CustomerId       string    `gorm:"type:varchar(255);not null;default:'';comment:客户ID" json:"customer_id"` // 付款用，paypal 和 airwallex 所需要的字段不一样，看情况加减字段
	CreatedAt        time.Time `gorm:"type:timestamp;not null;default:current_timestamp;comment:创建时间" json:"created_at"`
	UpdatedAt        time.Time `gorm:"type:timestamp;not null;default:current_timestamp on update current_timestamp;comment:更新时间" json:"updated_at"`
}

func (CreateTableUserSubscriptionLink2502201540) TableName() string {
	return "user_subscription_link"
}

func init() {
	Migrations = append(Migrations, &gormigrate.Migration{
		ID: "250220_1540_create_table_user_subscription_link",
		Migrate: func(tx *gorm.DB) error {
			return tx.Debug().AutoMigrate(&CreateTableUserSubscriptionLink2502201540{})
		},
		Rollback: func(tx *gorm.DB) error {
			return tx.Migrator().DropTable(&CreateTableUserSubscriptionLink2502201540{})
		},
	})
}
