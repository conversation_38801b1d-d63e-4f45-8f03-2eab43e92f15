package migration

import (
	"time"

	"github.com/go-gormigrate/gormigrate/v2"
	"gorm.io/gorm"
)

type CreateTableAdminAdsAccount2503201803 struct {
	Id          uint64    `gorm:"primary_key;autoIncrement:false;not null;default:0;comment:主键" json:"id"`
	AdminId     uint64    `gorm:"column:admin_id;type:bigint;not null;default:0;comment:管理员ID" json:"admin_id"`
	Platform    string    `gorm:"column:platform;type:varchar(50);not null;default:'';comment:广告平台" json:"platform"`
	AccountId   string    `gorm:"column:account_id;type:varchar(255);not null;default:'';comment:广告账户ID" json:"account_id"`
	AccessToken string    `gorm:"column:access_token;type:text;comment:访问令牌" json:"access_token"`
	CreatedAt   time.Time `gorm:"type:timestamp;not null;default:current_timestamp;comment:创建时间" json:"created_at"`
	UpdatedAt   time.Time `gorm:"type:timestamp;not null;index:idx_umts_ts;default:current_timestamp on update current_timestamp;comment:更新时间" json:"updated_at"`
}

func (CreateTableAdminAdsAccount2503201803) TableName() string {
	return "admin_ads_account"
}

func init() {
	Migrations = append(Migrations, &gormigrate.Migration{
		ID: "250320_1803_create_table_ads_account",
		Migrate: func(tx *gorm.DB) error {
			return tx.Debug().AutoMigrate(&CreateTableAdminAdsAccount2503201803{})
		},
		Rollback: func(tx *gorm.DB) error {
			return tx.Migrator().DropTable(&CreateTableAdminAdsAccount2503201803{})
		},
	})
}
