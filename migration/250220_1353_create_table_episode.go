package migration

import (
	"time"

	"github.com/go-gormigrate/gormigrate/v2"
	"gorm.io/gorm"
)

type CreateTableEpisode2502201353 struct {
	Id          uint64    `gorm:"primary_key;autoIncrement:false;not null;default:0;comment:主键" json:"id"`
	Vid         uint64    `gorm:"type:bigint;not null;default:0;index:idx_vdo_divvid;comment:视频ID" json:"vid"` // use as foreign key, reference to videos_normal.id or videos_hidden.id
	Title       string    `gorm:"type:varchar(255);not null;default:'';comment:标题" json:"title"`
	NeedLogin   bool      `gorm:"type:boolean;not null;default:true;comment:需要登录" json:"need_login"`
	NeedPay     bool      `gorm:"type:boolean;not null;default:true;comment:需要订阅" json:"need_pay"`
	CoverUrl    string    `gorm:"type:varchar(255);not null;default:'';comment:封面URL" json:"cover_url"`
	CaptionUrl  string    `gorm:"type:text;comment:字幕URL" json:"caption_url"`
	S3Url       string    `gorm:"type:varchar(255);not null;default:'';comment:S3 URL" json:"s3_url"`
	Sort        uint64    `gorm:"type:bigint;not null;default:0;comment:排序" json:"sort"`
	PublishedAt time.Time `gorm:"type:timestamp;not null;default:current_timestamp;comment:发布时间" json:"published_at"` // 发布时间，预留定时发布字段
	CreatedAt   time.Time `gorm:"type:timestamp;not null;default:current_timestamp;comment:创建时间" json:"created_at"`
	UpdatedAt   time.Time `gorm:"type:timestamp;not null;default:current_timestamp on update current_timestamp;comment:更新时间" json:"updated_at"`
}

func (CreateTableEpisode2502201353) TableName() string {
	return "episodes"
}

func init() {
	Migrations = append(Migrations, &gormigrate.Migration{
		ID: "250220_1353_create_table_episode",
		Migrate: func(tx *gorm.DB) error {
			return tx.Debug().AutoMigrate(&CreateTableEpisode2502201353{})
		},
		Rollback: func(tx *gorm.DB) error {
			return tx.Migrator().DropTable(&CreateTableEpisode2502201353{})
		},
	})
}
