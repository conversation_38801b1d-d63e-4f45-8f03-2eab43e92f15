package migration

import (
	"time"

	"github.com/go-gormigrate/gormigrate/v2"
	"gorm.io/gorm"
)

type CreateTableUserFavoLink2502201827 struct {
	Id        uint64    `gorm:"primary_key;autoIncrement:false;not null;default:0;comment:主键" json:"id"`
	UserId    uint64    `gorm:"type:bigint;not null;default:0;index:idx_ufl_uid;comment:用户ID" json:"user_id"`
	VideoId   uint64    `gorm:"type:bigint;not null;default:0;index:idx_ufl_vid;comment:视频ID" json:"video_id"`
	CreatedAt time.Time `gorm:"type:timestamp;not null;default:current_timestamp;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time `gorm:"type:timestamp;not null;default:current_timestamp on update current_timestamp;comment:更新时间" json:"updated_at"`
}

func (CreateTableUserFavoLink2502201827) TableName() string {
	return "user_favorite"
}

func init() {
	Migrations = append(Migrations, &gormigrate.Migration{
		ID: "250220_1827_create_table_user_favorite",
		Migrate: func(tx *gorm.DB) error {
			return tx.Debug().AutoMigrate(&CreateTableUserFavoLink2502201827{})
		},
		Rollback: func(tx *gorm.DB) error {
			return tx.Migrator().DropTable(&CreateTableUserFavoLink2502201827{})
		},
	})
}
