package migration

import (
	"time"

	"github.com/go-gormigrate/gormigrate/v2"
	"gorm.io/gorm"
)

type CreateTableVideo2502201331 struct {
	Id        uint64 `gorm:"primary_key;autoIncrement:false;not null;default:0;comment:主键" json:"id"`
	VideoName string `gorm:"type:varchar(255);index:idx_vdo_name;not null;default:'';comment:剧名" json:"video_name"`
	Cover     string `gorm:"type:varchar(255);not null;default:'';comment:封面" json:"cover"`
	// IsRecommend bool   `gorm:"type:boolean;not null;index:idx_vdo_rmd;default:false;comment:推荐" json:"is_recommend"`
	// CategoryId  uint64    `gorm:"type:bigint;not null;index:idx_vdo_ctg;default:0;comment:分类ID" json:"category_id"`
	PlayCount uint64 `gorm:"type:bigint;not null;default:0;comment:播放量" json:"play_count"`
	Favorite  uint64 `gorm:"type:bigint;not null;default:0;comment:收藏量" json:"favorite"`
	Share     uint64 `gorm:"type:bigint;not null;default:0;comment:分享量" json:"share"`
	// IsHot       bool      `gorm:"type:boolean;not null;default:false;index:idx_vdo_hot;comment:人工标注为最热门" json:"is_hot"`
	// IsChoice    bool      `gorm:"type:boolean;not null;default:false;index:idx_vdo_top;comment:人工标注为精选" json:"is_choice"`
	Description string    `gorm:"type:text;comment:描述" json:"description"`
	PublishedAt time.Time `gorm:"type:timestamp;not null;index:idx_vdo_pbl;default:current_timestamp;comment:发布时间" json:"published_at"` // 发布时间，预留定时发布字段
	CreatedAt   time.Time `gorm:"type:timestamp;not null;default:current_timestamp;comment:创建时间" json:"created_at"`
	UpdatedAt   time.Time `gorm:"type:timestamp;not null;default:current_timestamp on update current_timestamp;comment:更新时间" json:"updated_at"`
}

func (CreateTableVideo2502201331) TableName() string {
	return "videos_normal"
}

func init() {
	Migrations = append(Migrations, &gormigrate.Migration{
		ID: "250220_1331_create_table_video_normal",
		Migrate: func(tx *gorm.DB) error {
			return tx.Debug().AutoMigrate(&CreateTableVideo2502201331{})
		},
		Rollback: func(tx *gorm.DB) error {
			return tx.Migrator().DropTable(&CreateTableVideo2502201331{})
		},
	})
}
