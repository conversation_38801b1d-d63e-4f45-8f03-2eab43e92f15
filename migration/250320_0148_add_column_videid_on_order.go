package migration

import (
	"github.com/go-gormigrate/gormigrate/v2"
	"gorm.io/gorm"
)

type AddColumnVidEidOnOrder2503200148 struct {
	VideoId   uint64 `gorm:"column:video_id;type:bigint;index:idx_ord_vid;not null;default:0;comment:视频ID" json:"video_id"`
	EpisodeId uint64 `gorm:"column:episode_id;type:bigint;not null;default:0;comment:剧集ID" json:"episode_id"`
}

func (AddColumnVidEidOnOrder2503200148) TableName() string {
	return "payment_order"
}

func init() {
	Migrations = append(Migrations, &gormigrate.Migration{
		ID: "250320_0148_add_column_videid_on_order",
		Migrate: func(tx *gorm.DB) error {
			return tx.Debug().AutoMigrate(&AddColumnVidEidOnOrder2503200148{})
		},
		Rollback: func(tx *gorm.DB) error {
			return nil
		},
	})
}
