package migration

import (
	"github.com/go-gormigrate/gormigrate/v2"
	"gorm.io/gorm"
)

type AddColumnVaultIdOnOrder2503271146 struct {
	VaultId     string `gorm:"type:varchar(120);index:idx_po_vault_id;not null;default:'';comment:扣款凭证" json:"vault_id"`
	Fingerprint string `gorm:"type:varchar(120);index:idx_po_fgpt;not null;default:'';comment:指纹" json:"fingerprint"`
}

func (AddColumnVaultIdOnOrder2503271146) TableName() string {
	return "payment_order"
}

func init() {
	Migrations = append(Migrations, &gormigrate.Migration{
		ID: "250327_1146_add_column_vaultid_on_order",
		Migrate: func(tx *gorm.DB) error {
			return tx.Debug().AutoMigrate(&AddColumnVaultIdOnOrder2503271146{})
		},
		Rollback: func(tx *gorm.DB) error {
			return nil
		},
	})
}
