package migration

import (
	"time"

	"github.com/go-gormigrate/gormigrate/v2"
	"gorm.io/gorm"
)

type CreateTableWebsite2504071840 struct {
	Id        uint64    `gorm:"primaryKey;autoIncrement;not null;comment:ID" json:"id"`
	AdminId   uint64    `gorm:"type:bigint;index:idx_website_admin_id;not null;default:0;comment:管理员ID" json:"admin_id"`
	Website   string    `gorm:"type:varchar(180);not null;default:'';comment:网站" json:"website"`
	CreatedAt time.Time `gorm:"type:timestamp;not null;default:current_timestamp;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time `gorm:"type:timestamp;not null;default:current_timestamp on update current_timestamp;comment:更新时间" json:"updated_at"`
}

func (CreateTableWebsite2504071840) TableName() string {
	return "website"
}

func init() {
	Migrations = append(Migrations, &gormigrate.Migration{
		ID: "250407_1840_create_table_website",
		Migrate: func(tx *gorm.DB) error {
			return tx.Debug().AutoMigrate(&CreateTableWebsite2504071840{})
		},
		Rollback: func(tx *gorm.DB) error {
			return nil
		},
	})
}
